﻿using System;
using System.Collections.Generic;
using System.Reflection;
using System.Runtime.Serialization;

namespace PropertyLayers
{
    public class RankRuleOutput
    {
        public Int16 LeadRank { get; set; }
        public Int16 SpecialLeadRank { get; set; }
        public Int16 GroupIds { get; set; }
        public Int64 LeadId { get; set; }

    }
    [DataContract]
    [Serializable]
    //[BsonIgnoreExtraElements]
    public class LeadDetails
    {
        [DataMember]
        public LEADTYPE _LEADTYPE;
        
        [DataMember]
        public bool isUnsubscribed;

        [DataMember]
        public Int64 LeadId { get; set; }

        [DataMember]
        public Int32 Age { get; set; }

        [DataMember]
        public Int32 InsurerID { get; set; }

        [DataMember]
        public String PreviousBooking { get; set; }

        [DataMember]
        public String RepeatCustomer { get; set; }

        [DataMember]
        public Int64 AnnualIncome { get; set; }

        [DataMember]
        public Int16 CityID { get; set; }

        [DataMember]
        public String LeadSource { get; set; }

        [DataMember]
        public String Utm_source { get; set; }

        [DataMember]
        public String UTM_Medium { get; set; }

        [DataMember]
        public Int16 Covercount { get; set; }

        [DataMember]
        public Byte CMDOB { get; set; }

        [DataMember]
        public String Utm_campaign { get; set; }

        [DataMember]
        public Byte StateID { get; set; }

        [DataMember]
        public Boolean IsPED { get; set; }

        [DataMember]
        public String PEDTypes { get; set; }

        [DataMember]
        public String source { get; set; }

        [DataMember]
        public String Utm_term { get; set; }

        [DataMember]
        public String IsUnAssisted { get; set; }

        [DataMember]
        public Byte IsMarkExchange { get; set; }

        [DataMember]
        public short ChildCount { get; set; }

        [DataMember]
        public short AdultCount { get; set; }

        [DataMember]
        public string Country { get; set; }

        [DataMember]
        public DateTime CreatedOn { get; set; }

        [DataMember]
        public short LeadRank { get; set; }

        [DataMember]
        public byte ChatStatus { get; set; }

        [DataMember]
        public byte CommunicationId { get; set; }

        [DataMember]
        public String GroupCode { get; set; }

        [DataMember]
        public decimal LeadScore { get; set; }

        [DataMember]
        public short SpecialLeadRank { get; set; }

        [DataMember]
        public long AssigntoUserID { get; set; }

        [DataMember]
        public long AssignbyUserID { get; set; }

        [DataMember]
        public short JobID { get; set; }

        [DataMember]
        public short LastAssignedGroupID { get; set; }

        [DataMember]
        public short GroupID { get; set; }

        [DataMember]
        public short ProductID { get; set; }

        [DataMember]
        public byte AllocationTrackingEntryFlag { get; set; }

        [DataMember]
        public short AgentGrade { get; set; }

        [DataMember]
        public string AssignedToEmpId { get; set; }

        [DataMember]
        public short LeadGrade { get; set; }

        [DataMember]
        public short LastLeadRank { get; set; }

        [DataMember]
        public byte StatusId { get; set; }

        [DataMember]
        public short SubStatusId { get; set; }

        [DataMember]
        public long CustomerId { get; set; }

        [DataMember]
        public short SelectionCount { get; set; }

        [DataMember]
        public LeadDataStatus dataState { get; set; }

        [DataMember]
        public string FraudStatus { get; set; }

        [DataMember]
        public byte AssignedToGroup { get; set; }

        [DataMember]
        public short OutLeadRank { get; set; }

        [DataMember]
        public DateTime? CTCScheduleTime { get; set; }

        [DataMember]
        public bool IsAlreadyBooked { get; set; }

        public string MobileNo { get; set; }

        [DataMember]
        public short PayULeadRank { get; set; }

        [DataMember]
        public string EmailId { get; set; }

        [DataMember]
        public string Language { get; set; }

        [DataMember]
        public string ProcessName { get; set; }
        [DataMember]
        public bool IsNRI { get; set; }
        [DataMember]
        public long MobileSeries { get; set; }
        [DataMember]
        public long ClusterId { get; set; }

        [DataMember]
        public short IPCountryId { get; set; }

        [DataMember]
        public long Lowconversionvalue { get; set; }

        [DataMember]
        public string CustSource { get; set; }

        [DataMember]
        public short ChatLeadRank { get; set; }

        [DataMember]
        public bool IsAllocable { get; set; }

        [DataMember]
        public short InvestmentTypeID { get; set; }

        [DataMember]
        public bool IsChurn { get; set; }

        [DataMember]
        public string EmployeeID { get; set; }

        [DataMember]
        public bool IsMarineBooking { get; set; }

        [DataMember]
        public bool IsWorkManBooking { get; set; }

        [DataMember]
        public decimal? SA { get; set; }

        [DataMember]
        public string TypeOfPolicy { get; set; }

        [DataMember]
        public long UserID { get; set; }

        [DataMember]
        public long ReferralId { get; set; }

        [DataMember]
        public DateTime? PolicyExpiryDate { get; set; }

        [DataMember]
        public bool IsFos { get; set; }

        [DataMember]
        public int OccupancyId { get; set; }

        [DataMember]
        public string CustomerType { get; set; }

        [DataMember]
        public DateTime? ContinuePQ { get; set; }

        [DataMember]
        public int? TotalNoOfLives { get; set; }

        [DataMember]
        public int? RolloverCheck { get; set; }

        [DataMember]
        public short PolicyTenure { get; set; }

        [DataMember]
        public bool CustBooking { get; set; }

        [DataMember]
        public bool CPMRTO { get; set; }

        [DataMember]
        public bool IsAgentGrading { get; set; }

        [DataMember]
        public bool IsCMB { get; set; }
        [DataMember]
        public bool IsCPED { get; set; }
        [DataMember]
        public short PolicyType { get; set; }
        [DataMember]
        public string EmployeeRange { get; set; }
        [DataMember]
        public int TotalNoOFEmployees { get; set; }
        [DataMember]
        public string TransitType { get; set; }
        [DataMember]
        public string Custom_UTM { get; set; }
        [DataMember]
        public string UtmContent { get; set; }
        [DataMember]
        public string AssignmentType { get; set; } 
        [DataMember(EmitDefaultValue =false)]
        public long AssignedToAgent { get; set; }
        
        [DataMember]
        public int payu_affluence_score { get; set; }
        [DataMember]
        public int payu_propensity_score { get; set; }

        [DataMember]
        public string NriCity { get; set; }

        [DataMember]
        public byte UserConsent { get; set; }

        [DataMember]
        public short CustPrevProduct { get; set; }

        [DataMember]
        public string Name { get; set; }

        [DataMember]
        public bool IsCTCAssigned { get; set; } = false;

        public object this[string propertyName]
        {
            get
            {
                PropertyInfo property = GetType().GetProperty(propertyName);
                if (property == null) return null;
                return property.GetValue(this, null);
            }
            set
            {
                PropertyInfo property = GetType().GetProperty(propertyName);
                property.SetValue(this, value, null);
            }
        }
        [DataMember(EmitDefaultValue =false)]
        public Int64 ParentId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string AssignedToEcode { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string AssignedToAgentName { get; set; }
    }

    [DataContract]
    public class SpecialGroup
    {
        public Byte ProductId { get; set; }
        public Int16 GroupID { get; set; }
        public string GroupCode { get; set; }
        public bool IsAgent { get; set; }
        public bool IsGroup { get; set; }
    }

    [DataContract]
    public enum LeadDataStatus
    {
        DataPending = 0,
        DataLoaded = 1,
        LeadAssigned = 2
    }
    [DataContract]
    public enum LEADTYPE
    {
        NORMAL = 0,
        CHURN = 1,
    }
    public class FOSAgentDetails
    {
        public Int64 UserID { get; set; }
        public decimal LAT { get; set; }
        public decimal Long { get; set; }
    }
    public class FOSLeadDetails
    {
        public Int64 LeadID { get; set; }
        public decimal LAT { get; set; }

        public decimal Long { get; set; }
    }
    public class DistanceInfo
    {
        [DataMember(EmitDefaultValue = false)]
        public Int64 UserID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int64 LeadID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public decimal Distance { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public decimal CurrentLocationDistance { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int16 SlotId { get; set; }
        [DataMember(EmitDefaultValue = false)]
        public string LeadZone { get; set; }


    }
    public class AgentData
    {
        [DataMember(EmitDefaultValue = false)]
        public Int64 UserId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public decimal Distance { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public decimal CurrentLocationDistance { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int16 SlotLeadCount { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int16 FreshAssignSlotLeadCount { get; set; }
        public Int16 GroupId { get; set; }




    }
    public class LeadData
    {
        [DataMember(EmitDefaultValue = false)]
        public Int16 SlotId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string RegionArea { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public DateTime AppointmentDateTime { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public List<AgentData> AgentList { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int64 AllocableAgentId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int16 AllocableGroupId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public decimal MinDistance { get; set; }
    }
    public enum EnumGender
    {
        Male = 1,
        Female = 2,
        Uknown = 0
    }
    [DataContract]
    public class GroupCodeScriptGlobals
    {
        public LeadDetails lead;
        public long FLRC_ID;
        public string FLRC_GroupIds;
    }

    [DataContract]
    public class FOSReopenLeadConfig
    {
        public long FRLCId { get; set; }
        public int CityID { get; set; }
        public string ReopenProcess { get; set; }
        public short ProductID { get; set; }
        public string GroupIds { get; set; }
    }

    public class LeadAgentData
    {

        [DataMember(EmitDefaultValue = false)]
        public Int64 LeadID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int64 UserID { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int16 GroupId { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public decimal Distance { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int16 Priority { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int16 Grade { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public Int16 LeadRank { get; set; }


    }

    public class FOSRegionModel
    {
        [DataMember]
        public string FOSRegionName { get; set; }

        [DataMember]
        public decimal MaxDistance { get; set; }
    }

    [DataContract]
    [Serializable]
    public class AllocateLeadsData
    {
        //[DataMember]
        //List<long> LeadIds { get; set; }

        [DataMember]
        public long LeadId { get; set; }

        [DataMember]
        public bool ToAssign { get; set; }

        [DataMember]
        public string? AssignmentProcess { get; set; }

    }

    [DataContract]
    [Serializable]
    public class AllocateLeadResponse
    {
        [DataMember(EmitDefaultValue = false)]
        public List<LeadDetails> LeadDetails { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public string Error { get; set; }

        [DataMember(EmitDefaultValue = false)]
        public bool IsSuccess { get; set; }
        public Dictionary<long, string> LeadErrors { get; set; }
    }

    [DataContract]
    [Serializable]
    public class PayuScoresResponse
    {
        [DataMember]
        public string message { get; set; }
        [DataMember]
        public PayuScores data { get; set; }
        [DataMember]
        public string id { get; set; }
    }

    [DataContract]
    [Serializable]
    public class PayuScores
    {
        [DataMember]
        public int affluence_score { get; set; }
        [DataMember]
        public int pbhealth_gi_propensity_tier_calibrated { get; set; }
    }
    [DataContract]
    [Serializable]
    public class CustomerFeature
    {
        public const string affluenceScore = "AffluencePayU";
        public const string propensity = "PropensityPayU";
    }

    [DataContract]
    public class AssignedLeadData
    {
        public long LeadId { get; set; }
        public long AssignedAgent { get; set; }
        public long ParentId { get; set; }
    }

    public class GroupLeads
    {
        public int GroupId {  get; set; }
        public int LeadsAllocated { get; set; }
        public int GroupAllocationPercent { get; set; }
    }

    public class TriggeredLeads
    {
        public int TriggerSent { get; set; }
        public string TriggerName { get; set; }
        public int TriggerPercent { get; set; }
    }

    [DataContract]
    public class CallingData
    {
        [DataMember]
        public short CountryID { get; set; }

        [DataMember]
        public string MobileNumber { get; set; }

        [DataMember]
        public string CountryCode { get; set; }
    }

}