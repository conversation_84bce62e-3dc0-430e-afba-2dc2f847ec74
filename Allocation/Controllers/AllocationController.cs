﻿using Allocation.Helpers;
using EmailCommunicationBLL;
using Microsoft.AspNetCore.Mvc;
using PropertyLayers;

namespace Allocation.Controllers
{
    [CommAuth]
    [ApiController]
    [Route("allocation/api/[controller]/[action]")]
    
    public class AllocationController : ControllerBase
    {
        readonly IAllocationBLL objAllocationBLL;        
        public AllocationController(IAllocationBLL _objAllocationBLL)
        {
            objAllocationBLL = _objAllocationBLL;
        }
       
        [HttpGet]
        public string LeadsAllocation_health()
        {
            objAllocationBLL.LeadsAllocation_HealthAsync();
            return "true";
        }
        //[HttpGet]
        //public string GetTermPayURanks()
        //{
        //    //objAllocationBLL.LeadsAllocation_HealthAsync();
        //    TermPayURanksBLL.GetTermPayURanks();
        //    return "true";
        //}
        //[HttpGet("{LeadId}/{token}")]
        //public async Task<string> LeadsAllocationByLeadId_health(Int64 LeadId, string token)
        //{
        //    if(token != "jFge#%$HJ#K231!@#$") {
        //        return  "";
        //    }
        //    return await objAllocationBLL.HealthLeadAllocate(LeadId);
        //}

        [HttpPost]
        public async Task<AllocateLeadResponse> AllocateHealthLeads(AllocateLeadsData allocateLeadsData)
        {

            return await objAllocationBLL.HealthLeadAllocate(allocateLeadsData);
        }

        [HttpGet("{key}/{token}")]
        public short ClearCache(string key, string token)
        {
            return objAllocationBLL.ClearCache(key);
        }

        [HttpPost]
        public string Check(IFormFile KmlFile, int? CityId)
        {
            if (KmlFile == null || KmlFile.Length == 0)
            {
                return "";
                //return BadRequest("No file uploaded.");
            }

            var filePath = Path.GetTempFileName();

            using (var stream = new FileStream(filePath, FileMode.Create))
            {
                KmlFile.CopyTo(stream);
            }


            return objAllocationBLL.Test(filePath, CityId);
            //return "Works";

        }

        [HttpGet("{key}/{token}")]
        public bool CheckGetLeadsIncomeFromPayU(string key, string token)
        {
            if (token != "jFge#%hce$HJ#K71!@#$")
            {
                return false;
            }
            TermPayURanksBLL.GetLeadsIncomeFromPayU();
            return true;
        }

        [HttpPost]
        public bool GetPayuScoresForAllLeads()
        {
            return objAllocationBLL.GetPayuScoresForAllLeads();
        }

        [HttpPost]
        public bool GetPayuIncomeForAllLeads()
        {
            return objAllocationBLL.GetPayuIncomeForAllLeads();
        }

    }
}

