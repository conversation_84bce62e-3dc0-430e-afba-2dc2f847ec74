
{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information"
    }
  },
  "ValidHeaders": [ "agentid", "asterisktoken" ],
  "ValidOrigins": [ "https://progressiveqa.policybazaar.com", "localhost", "https://mobilematrix.policybazaar.com", "https://matrix.policybazaar.com", "https://matrixliveapi.policybazaar.com", "https://mobilematrixapi.policybazaar.com", "https://claim.policybazaar.com", "https://pbsupport.policybazaar.com", "https://matrixdashboard.policybazaar.com", "https://verification.policybazaar.com", "https://bms.policybazaar.ae", "https://pbmeet.policybazaar.com" ],
  "AllowedHosts": "*",
  "Communication": {
    "Environment": "UAT",
    "IsDevMode": "false",
    "ConnectionString": {
      "DEV": "Data Source=**********;Initial Catalog=PBCROMA;User Id=PBLive;Password=*********;",
      "UAT": "Data Source=qasqldb.policybazaar.com;Initial Catalog=PBCROMA;User Id=pbcroma;Password=*****************;Application Name=AllocationMatrix;",
      //"LIVE": "Data Source=PBAGL01.ETECHACES.COM;MultisubnetFailover=True;Database=PBCrelROMA;user Id=BackofficeSys;Password=***********;MultiSubnetFailover=True;",
      "LIVE": "Data Source=PBAGL01.ETECHACES.COM;MultisubnetFailover=True;Database=PBCROMA;user Id=Matrix_APP;Password=******$@!T@ad#@;MultiSubnetFailover=True;Application Name=AllocationMatrix;"
    },

    "ProductDBConnection": {
      "DEV": "Data Source=**********;Initial Catalog=PBCROMA;User Id=PBLive;Password=*********;",
      "UAT": "Data Source=qasqldb.policybazaar.com;Initial Catalog=PBCROMA;User Id=pbcroma;Password=*****************;Application Name=AllocationMatrix;",
      "LIVE": "Data Source=PBAGL01.ETECHACES.COM;MultisubnetFailover=True;Database=PBCROMA;user Id=BackofficeSys;Password=***********;MultiSubnetFailover=True;Application Name=AllocationMatrix;"
    },
    "ReplicaConnectionString": {
      "DEV": "Data Source=**********;Initial Catalog=PBCROMA;User Id=PBLive;Password=*********;",
      "UAT": "Data Source=qasqldb.policybazaar.com;Initial Catalog=PBCROMA;User Id=pbcroma;Password=*****************;Application Name=AllocationMatrix;",
      //"LIVE": "Data Source=PBSQL-PROD-SEC2.etechaces.com;MultisubnetFailover=True;Database=PBCROMA;user Id=Matrix_APP;Password=******$@!T@ad#@;MultiSubnetFailover=True;Application Name=AllocationMatrix;"
      "LIVE": "Data Source=PBAGL01.ETECHACES.COM;MultisubnetFailover=True;Database=PBCROMA;user Id=Matrix_APP;Password=******$@!T@ad#@;MultiSubnetFailover=True;Application Name=AllocationMatrix;"
    },
    "BMSsqlConnectionString": {
      "DEV": "Data Source=**********;Initial Catalog=PBCROMA;User Id=PBLive;Password=*********;",
      "UAT": "Data Source=qasqldb.policybazaar.com;Initial Catalog=COMM;User Id=Commproject;Password=*************;",
      "LIVE": "Data Source=communicationdb-prod.cjbifvsxxi4u.ap-south-1.rds.amazonaws.com;MultisubnetFailover=True;Database=COMM;user Id=MatrixComm;Password=************;MultiSubnetFailover=True;"
    },
    "RedisConnection": {
      "DEV": "matrixredis-qa-new-001.matrixredis-qa-new.kskv7l.aps1.cache.amazonaws.com:6379",
      "UAT": "matrixredis-qa-new-001.matrixredis-qa-new.kskv7l.aps1.cache.amazonaws.com:6379",
      "LIVE": "customerrevist.kskv7l.ng.0001.aps1.cache.amazonaws.com:6379,customerrevist-ro.kskv7l.ng.0001.aps1.cache.amazonaws.com:6379"
    },
    "MultiProdRedisConnection": {
      "DEV": "qa-matrix-lead-assignment-001.qa-matrix-lead-assignment.kskv7l.aps1.cache.amazonaws.com:6379",
      "UAT": "qa-matrix-lead-assignment-001.qa-matrix-lead-assignment.kskv7l.aps1.cache.amazonaws.com:6379",
      "LIVE": "master.prod-matrix-lead-assignment.kskv7l.aps1.cache.amazonaws.com:6379"
    },
    "MongoDBConnection": {
      "DEV": "matrixredis-qa.kskv7l.0001.aps1.cache.amazonaws.com:6379",
      "UAT": "************************************************************************;connectTimeoutms=40000&amp;socketTimeoutMS=40000",
      "LIVE": "***************************************************************************************************************************;replicaSet=rs3;readPreference=secondary;connectTimeoutms=8000;socketTimeoutMS=8000"


    },
    "MongoGridFSConnection": {
      "DEV": "matrixredis-qa.kskv7l.0001.aps1.cache.amazonaws.com:6379",
      "UAT": "*********************************************************************************;connectTimeoutms=40000&amp;socketTimeoutMS=8000",
      "LIVE": "customerrevist.kskv7l.ng.0001.aps1.cache.amazonaws.com:6379,customerrevist-ro.kskv7l.ng.0001.aps1.cache.amazonaws.com:6379"
    },
    "MongoLogDBConnection": {
      "DEV": "matrixredis-qa.kskv7l.0001.aps1.cache.amazonaws.com:6379",
      "UAT": "**********************************************************************;connectTimeoutms=8000;socketTimeoutMS=8000",
      "LIVE": "***********************************************************************************************************************************************************************************************"

    },
    "ChatDBConnection": {
      "DEV": "matrixredis-qa.kskv7l.0001.aps1.cache.amazonaws.com:6379",
      "UAT": "************************************************************************",
      "LIVE": "customerrevist.kskv7l.ng.0001.aps1.cache.amazonaws.com:6379,customerrevist-ro.kskv7l.ng.0001.aps1.cache.amazonaws.com:6379"
    },
    "OneLeadDBConnection": {
      "DEV": "******************************************************************",
      "UAT": "mongodb://oneLeadUsr:0neLea98jnjdhg3!@mdb-qa1.policybazaar.com:27017,mdb-qa2.policybazaar.com:27017,mdb-qa3.policybazaar.com:27017/oneLeadDB?authSource=oneLeadDB&replicaset=rs6&readPreference=secondaryPreferred&connectTimeoutMS=10000&socketTimeoutMS=6000&ssl=false",
      "LIVE": "***********************************************************************************************************;connectTimeoutms=8000;socketTimeoutMS=8000"
    },
    "RealTimeDBConnection": {
      "DEV": "matrixredis-qa.kskv7l.0001.aps1.cache.amazonaws.com:6379",
      "UAT": "*******************************************************************",
      "LIVE": "customerrevist.kskv7l.ng.0001.aps1.cache.amazonaws.com:6379,customerrevist-ro.kskv7l.ng.0001.aps1.cache.amazonaws.com:6379"
    },
    "KafkaBootstrapServers": {
      "DEV": "***********:9092",
      "UAT": "***********:9092",
      "LIVE": "***********:9092,************:9092,************:9092"
    },
    "KafkaUserName": {
      "DEV": "bmskfk",
      "UAT": "bmskfk",
      "LIVE": "bmskfk"
    },
    "KafkaPassword": {
      "DEV": "bkls76298764",
      "UAT": "bkls76298764",
      "LIVE": "bms76298764"
    },
    "RedisPassword": {
      "DEV": "hfS4X265vASb83F1LKags7BV",
      "UAT": "hfS4X265vASb83F1LKags7BV",
      "LIVE": "Fyt31c7B8gsS8B6a65vKJ"
    },
    "MultiProdAssigRedisPass": {
      "DEV": "ds437fghyDFM1tsa",
      "UAT": "ds437fghyDFM1tsa",
      "LIVE": "ds457fgghDAM1tzx"
    },
    "AWSSecretEnvironment": {
      "DEV": "QA_Matrix",
      "UAT": "QA_Matrix",
      "LIVE": "Prod_Matrix"
    },
    "MultiProdAWSSecretEnvironment": {
      "DEV": "MultiProdRedisQA",
      "UAT": "MultiProdRedisQA",
      "LIVE": "MultiProdRedisLive"
    },
    "IsAWSSceretEnabled": {
      "DEV": "true",
      "UAT": "true",
      "LIVE": "true"
    },
    "IsMultiProdAWSSceretEnabled" : {
      "DEV": "true",
      "UAT": "true",
      "LIVE": "true"
    },
    "AWSRedisPassKey" : {
      "DEV": "RedisPass",
      "UAT": "RedisPass",
      "LIVE": "RedisCustomerRevisitPass"
    },
    "Term_annual_income_updation_PROD_SQS": "https://sqs.ap-south-1.amazonaws.com/721537467949/term_annual_income_updation_PROD",

    "RealTimeDB": "RealTimeDB",
    "MongoDBCommunicationDB": "communicationD",
    "MongoGridFSDB": "communicationD",
    "MongoDBLogging": "logger",
    "ChatDB": "rocketchat_test",
    "ChatDBcommon": "rocketchat_test",
    "OneLeadDB": "oneLeadDB",
    "SaltKey": "PSVJQRk9QTEpNVU1DWUZCRVFGV1VVT0ZOV1RRU1NaWQ=",
    "InitializationVectorKey": "WVdsRkxWRVpaVUZOYVdsaA==",
    "SQSLogingQueueUrl": "https://sqs.ap-south-1.amazonaws.com/721537467949/LoggingQueue",
    "ConfigKey": "AllocationConfigValues",
    "MongoTimeOut": 10000,
    "VersioNum": "09-11-2022 22:41"


  }

}
