﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using DataAccessLayer;
using Newtonsoft.Json;
using PropertyLayers;
using MongoConfigProject;
using DataAccessLibrary;
using System.Dynamic;
using Helper;
using System.Threading.Tasks;
using System.Runtime.Caching;
using DataHelper;

namespace EmailCommunicationBLL
{

    public class AllocationBLL : IAllocationBLL
    {
        public static readonly DateTime ControllerLoadTime = DateTime.Now;
        private static readonly Dictionary<long, short> payUPropensityCache = new();

        private static readonly List<LeadDetails> leads = new();
        private static readonly List<LeadDetails> churnedLeadsForAllocation = new();
        private static DateTime lastLeadDateTime = DateTime.MinValue;
        static List<SpecialGroup> _SpecialGroups = null;
        private static DateTime NextDataRefreshDT = DateTime.MinValue;
        private static bool RefreshChurnData = true;
        private static short lastRunHour_AgentGrade = 0;
        Dictionary<long, string> LeadErrors;
        private static readonly Dictionary<short, short> payUPropensityAndLeadRankMapping = new(){
            { 10, 171 },{ 9, 172 },{ 8, 173 },{ 7, 174 },{ 6, 175 },{ 5, 176 },{ 4, 177 },{ 3, 178 },{ 2, 179 },{ 1, 180 }
        };
        private static readonly Dictionary<short, short> leadRankCTCMapping = new()
        {
            { 80, 80 },{ 88, 80 },{ 95, 80 },{ 96, 80 },{ 284, 80 },
            { 89, 291 },{ 90, 291 },{ 91, 291 },{ 92, 291 },{ 93, 291 },{ 283, 291 },{ 291, 291 },{ 292, 291 },{ 381, 291 },{ 382, 291 },{ 383, 291 },{ 384, 291 },{ 385, 291 },{ 386, 291 },{ 387, 291 },{ 388, 291 },{ 389, 291 },{ 390, 291 },{ 451, 291 },{ 452, 291 },{ 453, 291 },{ 454, 291 },{ 456, 291 },{ 457, 291 },{ 458, 291 },{ 459, 291 },{ 461, 291 },{ 462, 291 },{ 463, 291 },{ 464, 291 },
            { 71, 118 },{ 72, 118 },{ 73, 118 },{ 74, 118 },{ 76, 118 },{ 77, 118 },{ 78, 118 },{ 79, 118 },{ 116, 118 },{ 117, 118 },{ 118, 118 },{ 119, 118 },{ 280, 118 },{ 331, 118 },{ 332, 118 },{ 333, 118 },{ 334, 118 },{ 335, 118 },{ 336, 118 },{ 337, 118 },{ 338, 118 },{ 339, 118 },{ 340, 118 },{ 101, 118 },{ 102, 118 },{ 103, 118 },{ 104, 118 },{ 146, 118 },{ 147, 118 },{ 148, 118 },
            { 149, 118 },{ 110, 276 },{ 270, 276 },{ 275, 276 },{ 276, 276 },{ 285, 276 },{ 46, 123 },{ 47, 123 },{ 48, 123 },{ 67, 123 },{ 121, 123 },{ 122, 123 },{ 123, 123 },{ 124, 123 },{ 126, 123 },{ 127, 123 },{ 128, 123 },{ 281, 123 },{ 341, 123 },{ 342, 123 },{ 343, 123 },{ 344, 123 },{ 345, 123 },{ 346, 123 },{ 347, 123 },{ 348, 123 },{ 349, 123 },{ 350, 123 },{ 55, 249 },{ 56, 249 },
            { 57, 249 },{ 58, 249 },{ 68, 249 },{ 111, 249 },{ 112, 249 },{ 155, 249 },{ 156, 249 },{ 157, 249 },{ 158, 249 },{ 211, 249 },{ 212, 249 },{ 235, 249 },{ 236, 249 },{ 237, 249 },{ 238, 249 },{ 239, 249 },{ 240, 249 },{ 241, 249 },{ 242, 249 },{ 243, 249 },{ 246, 249 },{ 247, 249 },{ 248, 249 },{ 249, 249 },{ 250, 249 },{ 282, 249 },{ 391, 249 },{ 392, 249 },{ 393, 249 },{ 394, 249 },
            { 301, 249 },{ 302, 249 },{ 303, 249 },{ 304, 249 },{ 305, 249 },{ 306, 249 },{ 307, 249 },{ 308, 249 },{ 309, 249 },{ 310, 249 },{ 311, 249 },{ 312, 249 },{ 313, 249 },{ 314, 249 },{ 315, 249 },{ 316, 249 },{ 317, 249 },{ 318, 249 },{ 319, 249 },{ 320, 249 },{ 321, 249 },{ 322, 249 },{ 323, 249 },{ 324, 249 },{ 325, 249 },{ 326, 249 },{ 327, 249 },{ 328, 249 },{ 329, 249 },{ 330, 249 }
        };
        private static Int32 ReassignCount = 0;
        public static string[] IndiaCodes = { "392", "91", "999", "INDIA", "0", "NULL", "" };
        private static int[] NRIRanks = { 151, 152, 153 };
        private static int[] NRIRanksNew = { 501, 502 };
        private static Int32 payuExperimentFlag = 0;
        private static Int32 payuExperimentFlagHr = DateTime.Now.Hour;

        private static string[] PayURanksLeadSources = { "pb", "pbmobileapp", "pbmobile", "inbound", "whatsapp" };


        // Schedular
        public async Task<string> LeadsAllocation_HealthAsync()
        {

            short productId = 2;
            string Error = string.Empty;
            ReassignCount = 0;

            LeadErrors = new();
            DateTime requestTime = DateTime.Now;
            try
            {
                USP_UpdateAgentGrade_Auto(productId);
                USP_UpdateAgentGrade_Auto(productId, 1518);
            }
            catch (Exception ex)
            {
                string error = ex.ToString();
                LoggingHelper.LoggingHelper.AddloginQueue(null, 0, error, "USP_UpdateAgentGrade_Auto", "Allocation", "AllocationBLL", "", "", requestTime, DateTime.Now);
            }

            try
            {
                DateTime fetchLeadsFromDate = lastLeadDateTime;
                DateTime currDate = DateTime.Now;
                if (NextDataRefreshDT <= currDate && currDate.Hour % 6 == 0)
                {
                    lastLeadDateTime = DateTime.MinValue;
                    NextDataRefreshDT = DateTime.Now.AddDays(0.20);
                    RefreshChurnData = true;
                }

                DumpAgentsforAllocation(productId);


                // Fetch Leads for Allocation
                GetLeadsForAllocation(lastLeadDateTime, leads);
                await PopulateDataAndAllocateLeadAsync(leads);

                // Fetch Churn Leads for Allocation
                // if (RefreshChurnData || churnedLeadsForAllocation.Count == 0)
                // {
                GetChurnedLeadsForAllocation();
                //Console.WriteLine($"fetch Churn Leads {currDate}");
                // }

                // Do not Assign churnleads before 12noon
                // if (currDate.Hour >= 12 && currDate.Hour <= 23)
                await PopulateDataAndAllocateLeadAsync(churnedLeadsForAllocation);


                AllocationDLL.UpdateSchedulerendtime(productId);
                PurgeAssignedLeads();
                // return "100"; 
            }
            catch (Exception ex)
            {
                Error = ex.ToString();
            }
            finally
            {
                LoggingHelper.LoggingHelper.AddloginQueue(null, 0, Error, "GetAllocationRanks", "Allocation", "AllocationBLL", "", "", requestTime, DateTime.Now);
                if (LeadErrors.Count > 0)
                {
                    try
                    {
                        LoggingHelper.LoggingHelper.AddloginQueue(null, 0, JsonConvert.SerializeObject(LeadErrors), "Health Error leads", "Allocation", "AllocationBLL", "", "", DateTime.Now, DateTime.Now);
                    }
                    catch (Exception) { }
                }

            }

            return "Fin";
        }

        // API
        public async Task<AllocateLeadResponse> HealthLeadAllocate(AllocateLeadsData allocateLeadsData)
        {
            AllocateLeadResponse response = new();

            string Error = string.Empty;
            List<LeadDetails> leadList = new();
            LeadErrors = new();

            DateTime requestTime = DateTime.Now;

            try
            {
                GetLeadsForAllocation(DateTime.MinValue, leadList, allocateLeadsData.LeadId);
                await PopulateDataAndAllocateLeadAsync(leadList, allocateLeadsData);
            }
            catch (Exception ex)
            {
                Error = ex.ToString();
            }
            finally
            {

                HidePIIFromLeads(leadList);

                response.IsSuccess = true;
                response.LeadDetails = leadList;

                if (Error != "" || LeadErrors.Count > 0)
                {
                    response.IsSuccess = false;
                    response.LeadErrors = LeadErrors;
                    response.Error = Error;
                }


                LoggingHelper.LoggingHelper.AddloginQueue(null, allocateLeadsData.LeadId, Error, "HealthLeadAllocateAPI", "Allocation", "AllocationBLL", "", "HealthLeadAllocate", requestTime, DateTime.Now);
                if (LeadErrors.Count > 0)
                {
                    try
                    {
                        LoggingHelper.LoggingHelper.AddloginQueue(null, 0, JsonConvert.SerializeObject(LeadErrors), "Error leads - HealthLeadAllocateAPI", "Allocation", "AllocationBLL", "", "HealthLeadAllocate", DateTime.Now, DateTime.Now);
                    }
                    catch (Exception) { }
                }

            }
            return response;
        }


        private static void USP_UpdateAgentGrade_Auto(short productId, short groupId = 0)
        {
            DateTime CurrDate = DateTime.Now;
            short[] hours = { 10, 11, 12, 14, 18, 21 };

            if (
                hours.Contains(Convert.ToInt16(CurrDate.Hour))
                && (CurrDate.Minute >= 30 && CurrDate.Minute <= 45)
                && lastRunHour_AgentGrade != CurrDate.Hour
            )
            {
                AllocationDLL.USP_UpdateAgentGrade_Auto(productId, groupId);

                if (groupId != 0)
                {
                    // run for one time
                    lastRunHour_AgentGrade = (short)CurrDate.Hour;
                }
            }
        }

        private async Task PopulateDataAndAllocateLeadAsync(List<LeadDetails> leadList, AllocateLeadsData allocateLeadsData = null)
        {
            bool SkipAssignment = false;
            string AssignmentProcess = string.Empty;
            try
            {
                SkipAssignment = allocateLeadsData != null && !allocateLeadsData.ToAssign;
                AssignmentProcess = (allocateLeadsData == null || allocateLeadsData.AssignmentProcess == null) ? string.Empty : allocateLeadsData.AssignmentProcess;
                AssignmentProcess = AssignmentProcess.ToUpper();
            }
            catch { }

            foreach (LeadDetails lead in leadList)
            {
                try
                {
                    if (lead.dataState == LeadDataStatus.LeadAssigned)
                    {
                        continue;
                    }

                    lead.IsCTCAssigned = PopulateAssignmentDetailsForCTC(lead, AssignmentProcess);

                    if (lead._LEADTYPE != LEADTYPE.CHURN && 
                        (AssignmentProcess == "CTC" || !lead.isUnsubscribed) && 
                        !lead.IsCTCAssigned)
                    {
                        if (lead.FraudStatus == null || lead.FraudStatus == string.Empty)
                        {
                            CheckLeadFraud(lead);
                        }

                        if (lead.FraudStatus == "yes")
                        {
                            continue;
                        }


                        //if (lead.dataState == LeadDataStatus.DataPending)
                        PopulateLeadAdditionalDetails(lead);
                        PopulateLeadHealthDetails(lead);

                        if(lead.Country == "378" && !IsCorrectTimeToCall(lead.Country, lead.NriCity))
                        {
                            continue;
                        }

                        PopulateLeadAllocationKeyFactors(lead);
                        //PopulateLeadRank(lead);
                        PopulateLowConversionValue(lead);
                        PopulateCustSource(lead);
                        PopulatePayuScores(lead, AssignmentProcess);
                        PopulateLeadRankRuleEngine(lead);
                        PopulateSpecialLeadRank(lead);

                        PopulateLeadProcess(lead, AssignmentProcess);

                        PopulatePayuScoreForProcess(lead, AssignmentProcess);
                        
                        PopulateLeadScore(lead);
                        RankLeadByLeadScore(lead, AssignmentProcess);

                        GetModifiedLeadRankPayU(lead, AssignmentProcess);
                        await PopulateLeadGroupCodeAsync(lead);

                        // TEMP: LIMIT assignment of DhruvRathee leads
                        if (lead.Utm_source == "YT_NonBrand_DhruvRathee")
                        {
                            if (
                                !string.IsNullOrEmpty(lead.ProcessName) &&
                                ((lead.ProcessName.ToUpper() == "HEALTH-PED-SENIOR" && lead.LeadScore <= 16000)
                                || (lead.ProcessName.ToUpper() == "HEALTH-PED" && lead.LeadScore <= 16000)
                                || (lead.ProcessName.ToUpper() == "FF" && lead.LeadScore <= 16000)
                                || (lead.ProcessName.ToUpper() == "HEALTH-IND" && lead.LeadScore <= 14000)
                                )
                            )
                            {
                                // Console.WriteLine("DRT: " + lead.ProcessName +"; " + lead.LeadScore);
                                continue;
                            }

                        }
                        // Console.WriteLine("N: " + lead.ProcessName +"; " + lead.LeadScore);


                        HandleCTCLeads(lead, AssignmentProcess);

                        if (AssignmentProcess != "CTC")
                        {
                            ChkLeadAssignedinMultiproduct(lead);
                            CustomerIsBoookedAlready(lead);

                            List<string> NewChatRankingProcess = new() {
                                "HEALTH-CHAT"
                            };
                            if (
                                AssignmentProcess != ""
                                && !(AssignmentProcess == "CHAT" && NewChatRankingProcess.Contains(lead.ProcessName))
                            )
                            {
                                allocateLeadsData.AssignmentProcess = AssignmentProcess;
                                HandleAssignmentProcess(lead, allocateLeadsData);
                            }
                            if (lead.ProcessName == "HEALTH-CHAT")
                            {
                                lead.ChatLeadRank = lead.LeadRank;
                            }
                        }

                        //    UpdateLeadDataState(lead, LeadDataStatus.DataLoaded);

                    }

                    if (AssignmentProcess != "CTC" && lead.isUnsubscribed)
                    {
                        lead.LeadRank = 255;
                    }

                    if (lead._LEADTYPE == LEADTYPE.CHURN)
                    {
                        PopulateLeadAssignedAgent(lead);
                    }
                    if (ValidateLeadForAssignment(lead))
                    {
                        if (!lead.IsCTCAssigned)
                        {
                            GetAllocationAgentId(lead, AssignmentProcess); 
                        }
                        if (!SkipAssignment)
                        {
                            AllocateLead(lead, AssignmentProcess);

                            // DUMP LEAD DATA, Remove
                            AllocationDLL.DumpLeadDetails(lead, AssignmentProcess);
                        }

                        if (AssignmentProcess == "CHAT")
                        {
                            AllocationDLL.DumpLeadDetails(lead, AssignmentProcess);
                        }
                        ////////
                    }
                }
                catch (Exception ex)
                {
                    var err = ex.ToString();
                    LeadErrors.TryAdd(lead.LeadId, err);
                }
            }

        }

        private static bool PopulateAssignmentDetailsForCTC(LeadDetails lead, string AssignmentProcess)
        {
            if(AssignmentProcess != "CTC")
            {
                return false;
            }

            DataSet ds = AllocationDLL.PopulateAssignmentDetailsForCTC(lead);

            if(ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0
                && ds.Tables[0].Rows[0]["Status"] != DBNull.Value
                && Convert.ToBoolean(ds.Tables[0].Rows[0]["Status"])
            )
            {
                var row = ds.Tables[0].Rows[0];

                lead.AssigntoUserID = row["AssigntoUserID"] != DBNull.Value ? Convert.ToInt64(row["AssigntoUserID"]) : 0;
                lead.OutLeadRank = row["OutLeadRank"] != DBNull.Value ? Convert.ToInt16(row["OutLeadRank"]) : (short)0;
                lead.JobID = row["JobID"] != DBNull.Value ? Convert.ToInt16(row["JobID"]) : (short)0;

                return true;
            }
            return false;
        }

        private static void PopulatePayuScores(LeadDetails lead, string assignmentProcess)
        {
            DateTime ct = DateTime.Now;    

            if(assignmentProcess == "CTC")
            {
                return;
            }        

            if (ct.AddMinutes(5) <= lead.CTCScheduleTime)
            {
                return;
            }

            List<byte> regionalStates = new (){
                1,16,17,25,30,37,34
            };

            List<short> nonRegionalCities = new (){
                207,302,309,316,666
            };

            if(lead.LeadSource != null && PayURanksLeadSources.Contains(lead.LeadSource.ToLower())
                && assignmentProcess != "CHAT" &&
                (lead.IsPED || lead.Covercount > 1 || lead.StateID == 20)
                && ((!regionalStates.Contains(lead.StateID)) || nonRegionalCities.Contains(lead.CityID))
                && lead.IsNRI==false
            ){
                var payuScores = GetPayuScoreData(lead.CustomerId,lead.MobileNo,lead.EmailId);
                lead.payu_affluence_score = payuScores.affluence_score;
                lead.payu_propensity_score = payuScores.pbhealth_gi_propensity_tier_calibrated;
            }
        }

        private static void GetModifiedLeadRankPayU(LeadDetails lead, string assignmentProcess)
        {
            DateTime ct = DateTime.Now;
            int[] hitPayuForRanks = new[] { 348, 349, 350 };

            // TimeSpan start = new TimeSpan(10, 30, 0); // 10:30 AM
            // TimeSpan end = new TimeSpan(18, 30, 0);   // 6:30 PM

            // bool IsInTimeRange = ct.TimeOfDay >= start && ct.TimeOfDay <= end;
            if(assignmentProcess == "CTC")
            {
                return;
            }

            if (ct.AddMinutes(5) <= lead.CTCScheduleTime)
            {
                return;
            }

            if (
                (
                (lead.LeadRank >= 141 && lead.LeadRank <= 144) // FF
                || (lead.LeadRank >= 231 && lead.LeadRank <= 234) // IND
                || hitPayuForRanks.Contains(lead.LeadRank)
                //  || (lead.LeadRank >= 348 && lead.LeadRank <= 350 && lead.LeadId % 2 != 0) // Tamil FF
                 //|| (lead.LeadRank >= 201 && lead.LeadRank <= 205) // PED

                )
                && lead.LeadSource != null && PayURanksLeadSources.Contains(lead.LeadSource.ToLower())
                //&& payuExperimentFlag < 30
                //&& IsInTimeRange
                && assignmentProcess != "CHAT"
            )
            {
                var payuLeadRank = GetPayULeadRankV2(lead);

                //Console.WriteLine($"payuLeadRank: {payuLeadRank}");

                if (payuLeadRank > 0)
                {
                    lead.LeadRank = payuLeadRank;
                }
            }
        }

        private static void HandleAssignmentProcess(LeadDetails lead, AllocateLeadsData allocateLeadsData)
        {
            Dictionary<short, short> ChatRankMapping = new()
            {
                // PED
                { 201, 81 },{ 205, 81 },{ 202, 82 },{ 203, 83 },{ 204, 84 },

                // FF
                { 141, 81 },{ 142, 82 },{ 143, 83 },{ 144, 84 },

                // IND
                { 231, 81 },{ 232, 82 },{ 233, 83 },{ 234, 84 },

                // Telugu FF
                { 238, 81 },{ 239, 82 },{ 240, 83 },

                //Telugu Inf - Ind
                { 155, 81 },{ 156, 82 },{ 157, 83 },{ 158, 84 },

                // Telugu Inf - PED
                { 211, 81 },{ 212, 82 },

                // TELUGU-FF-INF
                { 246, 81 },{ 247, 82 },{ 248, 83 },

                // HEALTH-TELUGU-IND
                {235,81 }, {236,82}, {237, 83}
            };

            switch (allocateLeadsData.AssignmentProcess)
            {
                case "CHAT":
                    if (ChatRankMapping.ContainsKey(lead.LeadRank))
                    {
                        lead.ChatLeadRank = ChatRankMapping.GetValueOrDefault(lead.LeadRank);
                    }
                    //try
                    //{
                    //    LoggingHelper.LoggingHelper.AddloginQueue("", lead.LeadId, string.Empty, "ALLOCATION-LOG-CHAT", "Allocation", "AllocationBll", JsonConvert.SerializeObject(lead), $"{lead.ChatLeadRank}", DateTime.Now, DateTime.Now);
                    //}
                    //catch { }

                    break;
            }
        }

        private static void PopulateCustSource(LeadDetails lead)
        {
            var CustSource = AllocationDLL.GetCustSource(lead);
            lead.CustSource = CustSource;
        }

        private static void PopulateLowConversionValue(LeadDetails lead)
        {
            Int64 LowConversionValue = AllocationDLL.GetLowConversionValue(lead);
            lead.Lowconversionvalue = LowConversionValue;
        }

        private static void PopulateLeadProcess(LeadDetails lead, string AssignmentProcess)
        {
            var processName = HealthLeadRankRuleEngine.Execute(Engines.GetLeadProcess, lead);

            if (processName != "-1" && processName != "-2")
            {
                lead.ProcessName = processName;
            }
            else
            {
                lead.ProcessName = "";
            }

            // temporary for experiment
            // if (AssignmentProcess == "CHAT" && lead.LeadId % 2 == 0)
            // {
            //     lead.ProcessName = "HEALTH-CHAT";
            // }
        }

        //private static void PopulatePayULeadRank(LeadDetails lead)
        //{
        //    string[] skipPayURanksLeadSources = { "crosssell", "referral" };

        //    if (skipPayURanksLeadSources.Contains(lead.LeadSource.ToLower()))
        //    {
        //        return;
        //    }
        //    if (lead.LeadRank >= 171 && lead.LeadRank <= 180)
        //    {
        //        return;
        //    }

        //    var Payu_LeadRanks = "Payu_LeadRanks".AppSettings().Split(',');

        //    //if (Payu_LeadRanks.Contains(lead.LeadRank.ToString()) && lead.LeadRank == lead.SpecialLeadRank && lead.LeadId % 3 == 0)
        //    //{
        //        short payuLeadRank = GetPayULeadRank("**********", lead.EmailId, lead.LeadId);

        //        if (payuLeadRank != -1)
        //        {
        //            LoggingHelper.LoggingHelper.AddloginQueue("", lead.LeadId, string.Empty, "PayULeadRank", "Allocation", "", $"{lead.LeadRank}", $"{payuLeadRank}", DateTime.Now, DateTime.Now);
        //            lead.LeadGrade = lead.LeadRank;
        //            lead.LeadRank = payuLeadRank;
        //        }
        //    //}

        //}


        private static void PopulateLeadAssignedAgent(LeadDetails lead)
        {
            lead.AssigntoUserID = AllocationDLL.GetLeadAssignedAgent(lead.LeadId);
        }

        private static void PopulatePayuScoreForProcess(LeadDetails lead, string AssignmentProcess)
        {
            DateTime ct = DateTime.Now;
            string[] hitPayuForProcess = new []{ "FF", "HEALTH-IND", "HEALTH-MALAYALAM-FF", "HEALTH-MALAYALAM-PED", "HEALTH-MARATHI-PED", "HEALTH-MARATHI-IND", "HEALTH-MARATHI-FF",  "HEALTH-MALAYALAM-IND", "HEALTH-MALAYALAM-INF-FF", "HEALTH-MALAYALAM-INF-IND", "HEALTH-MALAYALAM-INF-PED", "HEALTH-NonRegional-INF-FF", "HEALTH-NonRegional-INF-IND", "HEALTH-NonRegional-INF-PED", "HEALTH-TAMIL-FF", "HEALTH-TAMIL-IND", "HEALTH-TAMIL-PED", "HEALTH-TELUGU-FF", "HEALTH-TELUGU-IND", "HEALTH-TELUGU-INF", "HEALTH-TELUGU-INF-FF", "HEALTH-TELUGU-INF-PED", "HEALTH-TELUGU-PED", "HEALTH-TELUGU-PED-1", "HEALTH-MALAYALAM-FF-1" };

            int[] hitPayuForRanks = new []{101, 102, 103, 104, 146, 147, 148, 149, 80, 88, 95, 96, 284, 110, 270, 275, 276, 285, 161,162,163,164,361,362,261,262,181,182};
            
            if(AssignmentProcess == "CTC"){
                return;
            }

            if (ct.AddMinutes(5) <= lead.CTCScheduleTime)
            {
                return;
            }

            if (AssignmentProcess != "CHAT"
                && lead.payu_affluence_score == -2 && lead.payu_propensity_score == -2
                && lead.LeadSource != null && PayURanksLeadSources.Contains(lead.LeadSource.ToLower())
                && (
                    (!string.IsNullOrEmpty(lead.ProcessName) && hitPayuForProcess.Contains(lead.ProcessName.ToUpper())) 
                    || hitPayuForRanks.Contains(lead.LeadRank) 
                )
            ){
                PayuScores scores = GetPayuScoreData(lead.CustomerId, lead.MobileNo, lead.EmailId);
                lead.payu_affluence_score = scores.affluence_score;
                lead.payu_propensity_score = scores.pbhealth_gi_propensity_tier_calibrated;
            }
        }

        private static void RankLeadByLeadScore(LeadDetails lead,string assignmentProcess = "")
        {
            Int16 LeadRank = 0;
            bool UseLeadScoreForRanking;
            if (assignmentProcess == "CHAT")
            {
                UseLeadScoreForRanking = true;
            }
            else
            {
                UseLeadScoreForRanking = HealthLeadRankRuleEngine.Execute(Engines.UseLeadScoreForRanking, lead) == "true";
            }

            if (UseLeadScoreForRanking)
            {
                // CustPrevProduct experiment on 50% of leads for HEALTH-TELUGU-PED process
                if (lead.ProcessName == "HEALTH-TELUGU-PED" && lead.LeadId % 2 != 0)
                {
                    lead.ProcessName = "HEALTH-TELUGU-PED-1";
                }

                LeadRank = AllocationDLL.GetLeadRankByLeadScore(lead);
            }

            if (LeadRank > 0)
            {
                lead.LeadRank = LeadRank;
                lead.SpecialLeadRank = LeadRank;
            }
                // if (lead.ProcessName != "" && lead.ProcessName != null)
                // {
                //     Console.WriteLine($": {lead.LeadId} -> {LeadRank}, score:{lead.LeadScore} : {lead.ProcessName}");
                // }
        }

        private static void DumpAgentsforAllocation(short ProductId)
        {
            AllocationDLL.DumpAgentsforAllocation(ProductId);
        }

        private static bool ValidateLeadForAssignment(LeadDetails lead)
        {
            bool isAssignable = lead.dataState != LeadDataStatus.LeadAssigned;

            // Do not assign booked leads
            if (lead.IsAlreadyBooked)
            {
                isAssignable = false;
                UpdateLeadDataState(lead, LeadDataStatus.LeadAssigned);
            }

            // Skip Manually or externally assigned churned leads
            if (lead._LEADTYPE == LEADTYPE.CHURN && lead.AssigntoUserID != 0)
            {
                isAssignable = false;
                UpdateLeadDataState(lead, LeadDataStatus.LeadAssigned);
            }
            return isAssignable;
        }

        private static void CustomerIsBoookedAlready(LeadDetails lead)
        {
            lead.IsAlreadyBooked = AllocationDLL.CustomerIsBoookedAlready(lead);
        }

        private static void ChkLeadAssignedinMultiproduct(LeadDetails lead)
        {
            bool isLeadAssignedInMultiProduct = AllocationDLL.ChkLeadAssignedinMultiproduct(lead.CustomerId, lead.ProductID, lead.LeadId);
            if (isLeadAssignedInMultiProduct)
            {
                lead.LeadRank = 200;
            }
        }

        private static void HandleCTCLeads(LeadDetails lead, string AssignmentProcess="")
        {
            string[] bypassCTCGroupCodes = { "PED Flag Team" };
            if (lead.LeadRank == 17)
            {
                return;
            }
            List<short> retainedLeadRanks = new() { 216, 217 };


            // return, if CTC is not scheduled
            if (AssignmentProcess != "CTC" && lead.CTCScheduleTime == null)
                return;

            DateTime currentDateTime = DateTime.Now;
            if (AssignmentProcess != "CTC" && currentDateTime.AddMinutes(5) <= lead.CTCScheduleTime)
            {
                lead.LeadRank = 0;
            }
            else if (retainedLeadRanks.Contains(lead.LeadRank)) {
                // Keep same leadranks
            }
            else if (lead.LeadRank >= 21 && lead.LeadRank <= 24)
            {
                lead.LeadRank = 21;
            }
            else if (lead.LeadRank >= 41 && lead.LeadRank <= 44)
            {
                lead.LeadRank = 41;
            }
            else if (lead.LeadRank == 213)
            {
                lead.LeadRank = 213;
            }
            else if (lead.LeadRank == 154)
            {
                lead.LeadRank = 154;
            }
            else if (lead.LeadRank == 251)
            {
                lead.LeadRank = 251;
            }
            else if (NRIRanks.Contains(lead.LeadRank))
            {
                if (lead.LeadId % 2 == 0)
                {
                    lead.LeadRank = 151;
                }
                else
                {
                    lead.LeadRank = 152;
                }
            }
            else if (NRIRanksNew.Contains(lead.LeadRank))
            {
                if (lead.LeadId % 2 == 0)
                {
                    lead.LeadRank = 501;
                }
                else
                {
                    lead.LeadRank = 502;
                }
            }
            else if (lead.LeadRank == 511 || lead.LeadRank == 512)
            {
                lead.LeadRank = lead.LeadRank;
            }
            else if (lead.LeadRank == 522) {
                lead.LeadRank = 522;
            }
            else if (lead.LeadRank == 521) {
                lead.LeadRank = 521;
            }
            else if (lead.IsNRI)
            {
                lead.LeadRank = 151;
            }
            else if (leadRankCTCMapping.ContainsKey(lead.LeadRank))
            {
                lead.LeadRank = leadRankCTCMapping.GetValueOrDefault(lead.LeadRank, (short)1);
            }
            else lead.LeadRank = 1;



            if (currentDateTime.AddMinutes(1) <= lead.CTCScheduleTime || bypassCTCGroupCodes.Contains(lead.GroupCode))
            { }
            else
            {
                lead.UTM_Medium = "CTC";
            }
        }

        private static void CheckLeadFraud(LeadDetails lead)
        {
            lead.FraudStatus = AllocationDLL.GetLeadFraud(lead);
            if (lead.FraudStatus == "none" && ChkFraudDetectionAPI(lead.LeadId, lead.CustomerId) == true)
            {
                lead.FraudStatus = "yes";
            }
        }

        private static void GetLeadsForAllocation(DateTime lastLeadDateTime, List<LeadDetails> leadList, Int64 LeadId = 0)
        {
            DataSet leadsDataSet = AllocationDLL.GetLeadsForAllocation(lastLeadDateTime, LeadId);


            if (leadsDataSet != null && leadsDataSet.Tables[0].Rows.Count != 0)
            {
                leadList.Clear();
                foreach (DataRow leadRow in leadsDataSet.Tables[0].Rows)
                {
                    LeadDetails leadData = new()
                    {
                        _LEADTYPE = LEADTYPE.NORMAL,
                        LeadId = (Int64)leadRow["LeadID"],
                        CustomerId = leadRow["CustomerID"] == null || leadRow["CustomerID"] == DBNull.Value ? (Int64)0 : (Int64)leadRow["CustomerID"],
                        Age = leadRow["Age"] != null && leadRow["Age"] != DBNull.Value ? (byte)leadRow["Age"] : (byte)0,
                        AnnualIncome = leadRow["AnnualIncome"] == DBNull.Value ? (Int64)0 : (Int64)leadRow["AnnualIncome"],
                        CityID = leadRow["CityID"] == null || leadRow["CityID"] == DBNull.Value ? (short)0 : Convert.ToInt16(leadRow["CityID"]),
                        StatusId = leadRow["StatusID"] != null && leadRow["StatusID"] != DBNull.Value ? (Byte)leadRow["StatusID"] : (Byte)0,
                        SubStatusId = leadRow["SubStatusID"] != null && leadRow["SubStatusID"] != DBNull.Value ? Convert.ToInt16(leadRow["SubStatusID"]) : (short)0,
                        LeadSource = leadRow["LeadSource"] == DBNull.Value ? String.Empty : leadRow["LeadSource"].ToString(),
                        Utm_source = leadRow["Utm_source"] == DBNull.Value ? String.Empty : leadRow["Utm_source"].ToString(),
                        UTM_Medium = leadRow["UTM_Medium"] == DBNull.Value ? String.Empty : leadRow["UTM_Medium"].ToString(),
                        Utm_campaign = leadRow["Utm_campaign"] == DBNull.Value ? String.Empty : leadRow["Utm_campaign"].ToString(),
                        UtmContent = leadRow["utm_content"] == DBNull.Value ? string.Empty : Convert.ToString(leadRow["utm_content"]),
                        StateID = leadRow["StateID"] != null && leadRow["StateID"] != DBNull.Value ? (Byte)leadRow["StateID"] : (Byte)0,
                        source = leadRow["source"] == DBNull.Value ? String.Empty : leadRow["source"].ToString(),
                        Utm_term = leadRow["utm_term"] == DBNull.Value ? String.Empty : leadRow["utm_term"].ToString(),
                        IsMarkExchange = leadRow["IsMarkExchange"] != null && leadRow["IsMarkExchange"] != DBNull.Value ? (Byte)leadRow["IsMarkExchange"] : (Byte)0,
                        CreatedOn = leadRow["CreatedOn"] != null && leadRow["CreatedOn"] != DBNull.Value ? Convert.ToDateTime(leadRow["CreatedOn"]) : DateTime.MinValue,
                        Country = leadRow["Country"] != null && leadRow["Country"] != DBNull.Value ? leadRow["Country"].ToString() : String.Empty,
                        ChatStatus = leadRow["ChatStatus"] != null && leadRow["ChatStatus"] != DBNull.Value ? (Byte)leadRow["ChatStatus"] : (Byte)0,
                        ProductID = leadRow["ProductID"] != null && leadRow["ProductID"] != DBNull.Value ? Convert.ToInt16(leadRow["ProductID"]) : (short)0,
                        LastLeadRank = leadRow["LeadGrade"] != null && leadRow["LeadGrade"] != DBNull.Value ? Convert.ToInt16(leadRow["LeadGrade"]) : (short)0,
                        CTCScheduleTime = leadRow["SecheduleTime"] != null && leadRow["SecheduleTime"] != DBNull.Value ? Convert.ToDateTime(leadRow["SecheduleTime"]) : null,
                        AssigntoUserID = leadRow["UserId"] != null && leadRow["UserId"] != DBNull.Value ? Convert.ToInt64(leadRow["UserId"]) : 0,
                        MobileNo = leadRow["MobileNo"] != null && leadRow["MobileNo"] != DBNull.Value ? leadRow["MobileNo"].ToString() : "",
                        EmailId = leadRow["Email"] != null && leadRow["Email"] != DBNull.Value ? leadRow["Email"].ToString() : "",
                        isUnsubscribed = leadRow["IsUnSubscribe"] != null && leadRow["IsUnSubscribe"] != DBNull.Value && Convert.ToBoolean(leadRow["IsUnSubscribe"]),
                        Name = leadRow["Name"] != null && leadRow["Name"] != DBNull.Value ? Convert.ToString(leadRow["Name"]) : string.Empty,
                        payu_affluence_score =-2,
                        payu_propensity_score=-2
                    };

                    // NRI lead flag: IsNRI
                    if (!IndiaCodes.Contains(leadData.Country))
                    {
                        leadData.IsNRI = true;
                    }

                    leadList.Add(leadData);
                    UpdateLastLeadDate(leadData);
                }
            }


            //return leads;

        }
        private static void GetChurnedLeadsForAllocation()
        {
            // clear previous data
            churnedLeadsForAllocation.Clear();

            // churned lead data
            DataSet leadsDataSet = AllocationDLL.GetChurnLeadsForAllocation();


            if (leadsDataSet != null && leadsDataSet.Tables[0].Rows.Count != 0)
            {
                foreach (DataRow leadRow in leadsDataSet.Tables[0].Rows)
                {
                    LeadDetails leadData = new()
                    {
                        _LEADTYPE = LEADTYPE.CHURN,
                        LeadId = (Int64)leadRow["LeadID"],
                        CustomerId = leadRow["CustomerID"] == null || leadRow["CustomerID"] == DBNull.Value ? (Int64)0 : Convert.ToInt64(leadRow["CustomerID"]),
                        //DOB //Age = leadRow["Age"] != null && leadRow["Age"] != DBNull.Value ? (byte)leadRow["Age"] : (byte)0,
                        AnnualIncome = leadRow["AnnualIncome"] == DBNull.Value ? (Int64)0 : Convert.ToInt64(leadRow["AnnualIncome"]),
                        //CityID = leadRow["CityID"] == null || leadRow["CityID"] == DBNull.Value ? (short)0 : Convert.ToInt16(leadRow["CityID"]),
                        StatusId = leadRow["StatusID"] != null && leadRow["StatusID"] != DBNull.Value ? (Byte)leadRow["StatusID"] : (Byte)0,
                        SubStatusId = leadRow["SubStatusID"] != null && leadRow["SubStatusID"] != DBNull.Value ? Convert.ToInt16(leadRow["SubStatusID"]) : (short)0,
                        LeadSource = leadRow["LeadSource"] == DBNull.Value ? String.Empty : leadRow["LeadSource"].ToString(),
                        Utm_source = leadRow["Utm_source"] == DBNull.Value ? String.Empty : leadRow["Utm_source"].ToString(),
                        UTM_Medium = leadRow["UTM_Medium"] == DBNull.Value ? String.Empty : leadRow["UTM_Medium"].ToString(),
                        Utm_campaign = leadRow["Utm_campaign"] == DBNull.Value ? String.Empty : leadRow["Utm_campaign"].ToString(),
                        //StateID = leadRow["StateID"] != null && leadRow["StateID"] != DBNull.Value ? (Byte)leadRow["StateID"] : (Byte)0,
                        //source = leadRow["source"] == DBNull.Value ? String.Empty : leadRow["source"].ToString(),
                        //Utm_term = leadRow["utm_term"] == DBNull.Value ? String.Empty : leadRow["utm_term"].ToString(),
                        //IsMarkExchange = leadRow["IsMarkExchange"] != null && leadRow["IsMarkExchange"] != DBNull.Value ? (Byte)leadRow["IsMarkExchange"] : (Byte)0,
                        CreatedOn = leadRow["CreatedON"] != null && leadRow["CreatedON"] != DBNull.Value ? Convert.ToDateTime(leadRow["CreatedON"]) : DateTime.MinValue,
                        //Country = leadRow["Country"] != null && leadRow["Country"] != DBNull.Value ? leadRow["Country"].ToString() : String.Empty,
                        //ChatStatus = leadRow["ChatStatus"] != null && leadRow["ChatStatus"] != DBNull.Value ? (Byte)leadRow["ChatStatus"] : (Byte)0,
                        ProductID = leadRow["ProductID"] != null && leadRow["ProductID"] != DBNull.Value ? Convert.ToInt16(leadRow["ProductID"]) : (short)0,
                        LastLeadRank = leadRow["LeadRank"] != null && leadRow["LeadRank"] != DBNull.Value ? Convert.ToInt16(leadRow["LeadRank"]) : (short)0,
                        //CTCScheduleTime = leadRow["SecheduleTime"] != null && leadRow["SecheduleTime"] != DBNull.Value ? Convert.ToDateTime(leadRow["SecheduleTime"]) : null,
                        //AssigntoUserID = leadRow["UserId"] != null && leadRow["UserId"] != DBNull.Value ? Convert.ToInt64(leadRow["UserId"]) : 0,
                        LeadRank = leadRow["LeadRank"] != null && leadRow["LeadRank"] != DBNull.Value ? Convert.ToInt16(leadRow["LeadRank"]) : (short)0,
                        LeadGrade = leadRow["LeadRank"] != null && leadRow["LeadRank"] != DBNull.Value ? Convert.ToInt16(leadRow["LeadRank"]) : (short)0,
                        LeadScore = leadRow["LeadScore"] != null && leadRow["LeadScore"] != DBNull.Value ? (decimal)leadRow["LeadScore"] : (decimal)0,
                        GroupCode = leadRow["GroupCode"] != null && leadRow["GroupCode"] != DBNull.Value ? leadRow["GroupCode"].ToString() : String.Empty,
                        LastAssignedGroupID = leadRow["ItemId"] != null && leadRow["ItemId"] != DBNull.Value ? Convert.ToInt16(leadRow["ItemId"]) : (short)0,
                    };

                    // Assign customercallback round the clock
                    // Start Churn lead Assignment after 12 noon
                    DateTime currDate = DateTime.Now;
                    if (
                        leadData.GroupCode == "customercallback"
                        || (currDate.Hour >= 12 && currDate.Hour <= 23)
                       )
                    {
                        churnedLeadsForAllocation.Add(leadData);
                        UpdateLeadDataState(leadData, LeadDataStatus.DataPending);
                        UpdateLastLeadDate(leadData);
                        // var idx = IsLeadDataAvailable(leadData, churnedLeadsForAllocation);

                        // if (idx < 0)
                        // {
                        //     UpdateLeadDataState(leadData, LeadDataStatus.DataPending);
                        //     churnedLeadsForAllocation.Insert(~idx, leadData);
                        //     UpdateLastLeadDate(leadData);
                        // }
                    }
                }
            }
            RefreshChurnData = false;
        }



        private static void PopulateLeadAllocationKeyFactors(LeadDetails leadData)
        {
            var KFAdataset = AllocationDLL.GetLeadAllocationKeyFactors(leadData.LeadId);
            if (KFAdataset != null)
            {
                var leadKFA = KFAdataset.Tables[0].Rows[0];
                leadData.PreviousBooking = (leadKFA["PreviousBooking"] == null || leadKFA["PreviousBooking"] == DBNull.Value) ? String.Empty : Convert.ToString(Convert.ToInt16(leadKFA["PreviousBooking"]));
                leadData.RepeatCustomer = (leadKFA["RepeatCustomer"] == null || leadKFA["RepeatCustomer"] == DBNull.Value) ? String.Empty : Convert.ToString(Convert.ToInt16(leadKFA["RepeatCustomer"]));
                leadData.InsurerID = (leadKFA["InsurerID"] == null || leadKFA["InsurerID"] == DBNull.Value) ? 0 : (Int32)leadKFA["InsurerID"];
            }
            else
            {
                leadData.PreviousBooking = "0";
                leadData.RepeatCustomer = "0";
                leadData.InsurerID = 0;
            }
        }

        private static void PopulateLeadHealthDetails(LeadDetails leadData)
        {
            var healthData = AllocationDLL.GetLeadHealthDetails(leadData.LeadId);
            if (healthData != null)
            {
                var HDRow = healthData.Tables[0].Rows[0];
                leadData.Covercount = HDRow["Covercount"] == DBNull.Value ? (Int16)0 : Convert.ToInt16(HDRow["Covercount"]);
                leadData.CMDOB = HDRow["MaxCMAge"] == DBNull.Value ? Convert.ToByte(0) : (Byte)HDRow["MaxCMAge"];
                leadData.ChildCount = HDRow["child"] == DBNull.Value ? Convert.ToInt16(0) : (Int16)HDRow["child"];
                leadData.AdultCount = HDRow["adult"] == DBNull.Value ? Convert.ToInt16(0) : (Int16)HDRow["adult"];
                leadData.CommunicationId = HDRow["CommunicationID"] == DBNull.Value ? (Byte)0 : (Byte)HDRow["CommunicationID"];
                leadData.Language = HDRow["Language"] == DBNull.Value ? String.Empty : Convert.ToString(HDRow["Language"]);
                leadData.MobileSeries = HDRow["MobileSeries"] == DBNull.Value ? 0 : (Int64)HDRow["MobileSeries"];
                leadData.ClusterId = HDRow["ClusterId"] == DBNull.Value ? 0 : (Int64)HDRow["ClusterId"];
                leadData.IPCountryId = HDRow["IPCountryId"] == DBNull.Value ? Convert.ToInt16(0) : (short)HDRow["IPCountryId"];
                leadData.NriCity = HDRow["NriCity"] == DBNull.Value ? "Default" : Convert.ToString(HDRow["NriCity"]);
                leadData.UserConsent = HDRow["UserConsent"] == DBNull.Value ? Convert.ToByte(0) : Convert.ToByte(HDRow["UserConsent"]);
            }
            else
            {
                leadData.Language = "";
            }
        }

        private static void PopulateLeadAdditionalDetails(LeadDetails leadData)
        {
            var additionalLeadDetails = AllocationDLL.GetAdditionalLeadDetails(leadData.LeadId, leadData.CustomerId);
            if (additionalLeadDetails != null)
            {
                var ALDRow = additionalLeadDetails.Tables[0].Rows[0];

                leadData.IsUnAssisted = ALDRow["IsUnAssisted"] == DBNull.Value || ALDRow["IsUnAssisted"] == null ? String.Empty : ALDRow["IsUnAssisted"].ToString();
                leadData.IsPED = ALDRow["IsPED"] != DBNull.Value && ALDRow["IsPED"] != null && Convert.ToBoolean(ALDRow["IsPED"]);
                leadData.PEDTypes = ALDRow["PEDTypes"] == DBNull.Value || ALDRow["PEDTypes"] == null ? String.Empty : Convert.ToString(ALDRow["PEDTypes"]);
                leadData.CustPrevProduct = ALDRow["CustPrevProduct"] == DBNull.Value || ALDRow["CustPrevProduct"] == null ? (short)0 : Convert.ToInt16(ALDRow["CustPrevProduct"]);
            }
        }

        private static void PopulateLeadRank(LeadDetails lead)
        {
            // skip leadrank if payu leadrank is present
            if (lead.LeadRank >= 171 && lead.LeadRank <= 180)
            {
                return;
            }

            Int16 LeadRank = AllocationDLL.GetLeadRank(lead);

            lead.LeadRank = LeadRank;
        }
        private static void PopulateLeadRankRuleEngine(LeadDetails lead)
        {
            Int16 LeadRank = 0;
            string err = string.Empty;
            var requestTime = DateTime.Now;
            string CoreProcess = "-1";
            try
            {
                Dictionary<string, dynamic> RE_results = new();
                int LeadRankCategory = 0;

                // Adhoc Leadranks
                LeadRank = Convert.ToInt16(HealthLeadRankRuleEngine.Execute(Engines.AdhocRanks, lead));

                // Core And Non Core ranks
                if (LeadRank < 0)
                {
                    // Get lead 
                    CoreProcess = HealthLeadRankRuleEngine.Execute(Engines.CheckCoreLeads, lead);

                    // Core Leads - Category
                    if (CoreProcess == Processes.CORE)
                    {
                        if (LeadRankCategory <= 0) LeadRankCategory = Convert.ToInt32(HealthLeadRankRuleEngine.Execute(Engines.CoreRanking1, lead));
                        if (LeadRankCategory <= 0) LeadRankCategory = Convert.ToInt32(HealthLeadRankRuleEngine.Execute(Engines.CoreRanking2, lead));
                        if (LeadRankCategory <= 0) LeadRankCategory = Convert.ToInt32(HealthLeadRankRuleEngine.Execute(Engines.CoreRanking3, lead));
                        if (LeadRankCategory <= 0) LeadRankCategory = 4;

                        LeadRank = Convert.ToInt16(HealthLeadRankRuleEngine.Execute($"{Engines.RanksCoreRankingN}{LeadRankCategory}", lead));
                    }
                    else
                    {
                        // pre non-core rules
                        LeadRank = Convert.ToInt16(HealthLeadRankRuleEngine.Execute(Engines.RanksNonCoreRankingPre, lead));

                        // main non-core rules
                        if (LeadRank <= 0)
                        {
                            // Non Core Leads - Category
                            if (LeadRankCategory <= 0) LeadRankCategory = Convert.ToInt32(HealthLeadRankRuleEngine.Execute(Engines.NonCoreRanking1, lead));
                            if (LeadRankCategory <= 0) LeadRankCategory = Convert.ToInt32(HealthLeadRankRuleEngine.Execute(Engines.NonCoreRanking2, lead));
                            if (LeadRankCategory <= 0) LeadRankCategory = Convert.ToInt32(HealthLeadRankRuleEngine.Execute(Engines.NonCoreRanking3, lead));
                            if (LeadRankCategory <= 0) LeadRankCategory = Convert.ToInt32(HealthLeadRankRuleEngine.Execute(Engines.NonCoreRanking4, lead));
                            if (LeadRankCategory > 0)
                            {
                                LeadRank = Convert.ToInt16(HealthLeadRankRuleEngine.Execute($"{Engines.RanksNonCoreRankingN}{LeadRankCategory}", lead));
                            }
                        }

                        // post non-core rules
                        if (LeadRankCategory <= 0 && LeadRank <= 0)
                        {
                            LeadRank = Convert.ToInt16(HealthLeadRankRuleEngine.Execute(Engines.RanksNonCoreRankingPost, lead));
                        }
                    }
                    //}
                    // if(LeadRank == -1)
                    //RE_results.Add("IsCoreLead", CoreProcess == Processes.CORE);
                    //RE_results.Add("CoreLeadRank", LeadRank);
                    //lead.LeadRank = AllocationDLL.GetLeadRankV2(lead, RE_results, 0);
                }
                RE_results.Add("IsCoreLead", CoreProcess == Processes.CORE);
                RE_results.Add("CoreLeadRank", LeadRank);
                //lead.LeadRank = AllocationDLL.GetLeadRankV2(lead, RE_results, 0);

                lead.LeadRank = LeadRank;
            }
            catch (Exception e)
            {
                err = e.ToString();
                LoggingHelper.LoggingHelper.AddloginQueue(null, lead.LeadId, err, "PopulateLeadRankRuleEngine", "Allocation", "AllocationBLL", "", $"{LeadRank}", requestTime, DateTime.Now);
            }
            finally
            {
                if (LeadRank == -1)
                {
                    LoggingHelper.LoggingHelper.AddloginQueue(null, lead.LeadId, err, "LEAD_RANK_-1", "Allocation", "AllocationBLL", $"Original: {lead.LeadRank}", $"Ruleengine: {LeadRank}", requestTime, DateTime.Now);
                }
                if (LeadRank != lead.LeadRank)
                {
                    LoggingHelper.LoggingHelper.AddloginQueue(null, lead.LeadId, err, "LEAD_RANK_MISMATCH", "Allocation", "AllocationBLL", $"Original: {lead.LeadRank}", $"Ruleengine: {LeadRank}", requestTime, DateTime.Now);
                }
                //else
                //{
                //    Console.WriteLine($"Correct: {lead.LeadId} -> {LeadRank}");
                //}
            }

        }
        private static void PopulateSpecialLeadRank(LeadDetails lead)
        {
            lead.SpecialLeadRank = lead.LeadRank;

            // SPECIAL LEADRANK not used in health, use rule engine to generate if required later
            //Int16 SpecialLeadRank = AllocationDLL.GetLeadRank(lead, 1);
            //lead.SpecialLeadRank = SpecialLeadRank;
        }


        private static async Task PopulateLeadGroupCodeAsync(LeadDetails lead)
        {
            var GroupCode = AllocationDLL.GetLeadGroupCode(lead);
            lead.GroupCode = GroupCode;
            // try
            // {
            //     var FOSReopenLeadConfigList = GetFosReopenLeadConfig();

            //     string code = "GetGroupCode_dotnetcode".AppSettings();
            //     GroupCodeScriptGlobals groupCodeScriptGlobals = new()
            //     {
            //         lead = lead,
            //     };

            //     foreach (FOSReopenLeadConfig fOSReopenLeadConfig in FOSReopenLeadConfigList)
            //     {
            //         if (fOSReopenLeadConfig.ReopenProcess == lead.Utm_source && fOSReopenLeadConfig.CityID == lead.CityID)
            //         {
            //             groupCodeScriptGlobals.FLRC_ID = fOSReopenLeadConfig.FRLCId;
            //             groupCodeScriptGlobals.FLRC_GroupIds = fOSReopenLeadConfig.GroupIds;
            //         }
            //     }

            //     string GroupCode_V2 = String.Empty;
            //     try
            //     {
            //         GroupCode_V2 = await ExecuteString.ExecuteCodeAsync<string>(code, new List<string> { }, groupCodeScriptGlobals);
            //         if (GroupCode != GroupCode_V2)
            //         {
            //             LoggingHelper.LoggingHelper.AddloginQueue("", lead.LeadId, string.Empty, "GroupCode_V2", "Allocation", "", "GroupCode not matching", $"{GroupCode}, {GroupCode_V2}", DateTime.Now, DateTime.Now);
            //         }
            //     }
            //     catch (Exception ex)
            //     {
            //         LoggingHelper.LoggingHelper.AddloginQueue("", lead.LeadId, ex.ToString(), "GroupCode_V2", "Allocation", "", "Script Error", string.Empty, DateTime.Now, DateTime.Now);
            //     }

            // }
            // catch (Exception ex)
            // {
            //     LoggingHelper.LoggingHelper.AddloginQueue("", lead.LeadId, ex.ToString(), "GroupCode_V2", "Allocation", "", "PopulateLeadGroupCodeAsync", string.Empty, DateTime.Now, DateTime.Now);
            // }
        }


        private static void PopulateLeadScore(LeadDetails lead)
        {
            if (lead.LeadRank >= 191 && lead.LeadRank <= 199)
            {
                return;
            }
            if (lead.LeadRank >= 600 && lead.LeadRank <= 609)
            {
                return;
            }
            var LeadScore = AllocationDLL.GetLeadScore(lead);
            lead.LeadScore = LeadScore;

        }
        private static void GetAllocationAgentId(LeadDetails lead, string AssignmentProcess="")
        {
            Int16 OutLeadRank = 0;
            Int64 UserID = 0;
            string AssignedToEcode = string.Empty;
            string AssignedToAgentName = string.Empty;

            lead.AssignbyUserID = 124;
            //lead.LastAssignedGroupID = 0;
            lead.AssignedToGroup = 0;

            if (_SpecialGroups == null || _SpecialGroups.Count == 0)
            {
                _SpecialGroups = AllocationDLL.GetSpecialGroups();
            }


            if (lead.GroupCode != null && lead.GroupCode.ToUpper().Contains("3DAYCHURN"))
            {
                if (ReassignCount == 0)
                {
                    AllocationDLL.ResetAgentBatchAssignlimit();
                    ReassignCount = ReassignCount + 1;
                }
            }

            if (lead.GroupCode != null && lead.GroupCode.ToUpper() == "3DAYCHURN")
            {
                lead.JobID = 31; // agent on leave churn                        
                UserID = AllocationDLL.GetAgentID_HealthAllocation(1, lead.LeadRank, lead.InsurerID, lead.CreatedOn, lead.Age, ref OutLeadRank, ref AssignedToEcode, ref AssignedToAgentName, 0, lead.GroupCode, lead.LastAssignedGroupID, 0);
                lead.AssigntoUserID = UserID;
            }
            else if (lead.GroupCode != null && lead.GroupCode.ToUpper() == "3DAYCHURNLEAD")
            {
                lead.JobID = 34; // agent on leave churn                        
                UserID = AllocationDLL.GetAgentID_HealthAllocation(2, lead.LeadRank, lead.InsurerID, lead.CreatedOn, lead.Age, ref OutLeadRank, ref AssignedToEcode, ref AssignedToAgentName, 0, lead.GroupCode, lead.LastAssignedGroupID, 0);
                lead.AssigntoUserID = UserID;
            }
            else if (lead.GroupCode != null && lead.GroupCode.ToLower() == "customercallback")
            {
                lead.JobID = 85; // agent on leave churn                        
                UserID = AllocationDLL.GetAgentID_HealthAllocation(3, lead.LeadRank, lead.InsurerID, lead.CreatedOn, lead.Age, ref OutLeadRank, ref AssignedToEcode, ref AssignedToAgentName, 0, lead.GroupCode, lead.LastAssignedGroupID, 0);
                lead.AssigntoUserID = UserID;
            }
            else if (lead.AssigntoUserID > 0)
            {
                //lead is already assigned to some agent 
                UserID = lead.AssigntoUserID;
            }
            else if (!string.IsNullOrEmpty(lead.GroupCode) && lead.GroupCode.Contains(','))
            {
                lead.JobID = 3;
                UserID = AllocationDLL.GetAgentID_HealthAllocation(0, lead.SpecialLeadRank, lead.InsurerID, lead.CreatedOn, lead.Age, ref OutLeadRank, ref AssignedToEcode, ref AssignedToAgentName, 0, lead.GroupCode, lead.LastAssignedGroupID, 0);
                lead.AssigntoUserID = UserID;
            }
            else if (!string.IsNullOrEmpty(lead.GroupCode))
            {
                lead.JobID = 3;
                var specialGroup = _SpecialGroups.Where(p => p.GroupCode == lead.GroupCode && p.ProductId == 2).SingleOrDefault();
                if (specialGroup != null)
                {
                    if (specialGroup.IsAgent == true)
                    {
                        UserID = AllocationDLL.GetAgentID_HealthAllocation(specialGroup.GroupID, lead.LeadRank, lead.InsurerID, lead.CreatedOn, lead.Age, ref OutLeadRank, ref AssignedToEcode, ref AssignedToAgentName, 0, lead.GroupCode, lead.LastAssignedGroupID, 0);
                        lead.GroupID = specialGroup.GroupID;
                        lead.AssigntoUserID = UserID;
                    }
                    else if (specialGroup.IsAgent == false && specialGroup.IsGroup == true)
                    {
                        lead.AssignedToGroup = 1;
                        lead.GroupID = specialGroup.GroupID;
                        lead.AssigntoUserID = 0;
                    }

                    //overflowlogic
                    if (UserID == 0 && (lead.GroupCode.ToUpper() == "FOS APPOINTMENT GROUP" && DateTime.Now.Hour > 10))
                    {
                        UserID = AllocationDLL.GetAgentID_HealthAllocation(-1, lead.LeadRank, lead.InsurerID, lead.CreatedOn, lead.Age, ref OutLeadRank, ref AssignedToEcode, ref AssignedToAgentName, 0, lead.GroupCode, lead.LastAssignedGroupID, 0);
                        lead.AssigntoUserID = UserID;
                        lead.JobID = 5;
                    }
                }
            }
            else
            {
                lead.JobID = 8;
                if (lead.UTM_Medium == "CTC")
                    lead.JobID = 12;
                
                if(AssignmentProcess == "CTC")
                {
                    lead.JobID = 13;
                }

                UserID = AllocationDLL.GetAgentID_HealthAllocation(-1, lead.LeadRank, lead.InsurerID, lead.CreatedOn, lead.Age, ref OutLeadRank, ref AssignedToEcode, ref AssignedToAgentName, 0, lead.GroupCode, lead.LastAssignedGroupID, lead.SpecialLeadRank);
                lead.AssigntoUserID = UserID;

                //if (UserID > 0 && coreLeadRanks.Contains(lead.LeadRank) && isAI == true)
                //    coreLeadCount = coreLeadCount + 1;

            }
            lead.OutLeadRank = OutLeadRank;
            lead.AssignedToEcode = AssignedToEcode;
            lead.AssignedToAgentName = AssignedToAgentName;
        }

        private static void SetUserProperties(Int64 UserID, LeadDetails _AllocationDetails, string AssignmentProcess="")
        {
            //var Specialgroups = (_AllocationDetails.ProductID.ToString() + "_AllocationSpecialGroup").AppSettings().Split(',');
            _AllocationDetails.AssignbyUserID = AssignmentProcess == "CTC" ? 1 : 124;
            _AllocationDetails.AssigntoUserID = UserID;
            var ds = AllocationDLL.GetAgentdetails(UserID);
            if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
            {
                _AllocationDetails.AgentGrade = Convert.ToInt16(ds.Tables[0].Rows[0]["Grade"]);
                _AllocationDetails.GroupID = Convert.ToInt16(ds.Tables[0].Rows[0]["GroupId"]);
                _AllocationDetails.AssignedToEmpId = Convert.ToString(ds.Tables[0].Rows[0]["AgentCode"]);
            }
            else
            {
                ds = AllocationDLL.GetAssignedAgentdetails(UserID);
                if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
                {
                    _AllocationDetails.AgentGrade = Convert.ToInt16(ds.Tables[0].Rows[0]["Grade"]);
                    _AllocationDetails.GroupID = Convert.ToInt16(ds.Tables[0].Rows[0]["GroupId"]);
                    _AllocationDetails.AssignedToEmpId = Convert.ToString(ds.Tables[0].Rows[0]["AgentCode"]);
                }
            }
        }

        public static bool ChkFraudDetectionAPI(long LeadID, long CustomerID)
        {
            DateTime requestTime = DateTime.Now;
            try
            {
                string URL = "AIFraudAPIURL".AppSettings();
                string Token = "AIFraudAPToken".AppSettings();
                Dictionary<object, object> header = new()
                {
                    { "x-access-token", Token }
                };
                dynamic obj = new ExpandoObject();
                obj.leadid = LeadID;
                obj.customerid = CustomerID;
                obj.mobile_no = "";
                obj.emailid = "";
                dynamic _data = JsonConvert.DeserializeObject<dynamic>(CommonAPICall.CallAPI(URL, JsonConvert.SerializeObject(obj), "POST", 1000, "application/json", header));

                if (_data != null && _data["score"] != null)
                {
                    bool IsFraudCase = false;
                    string Category = _data["score"];
                    if (Category != "Pass")
                        IsFraudCase = true;
                    AllocationDLL.UpdateFraudDetectionData(LeadID, IsFraudCase, Category);
                    return IsFraudCase;
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", LeadID, ex.ToString(), "chkFraudDetection", "Allocation", "", string.Empty, string.Empty, requestTime, DateTime.Now);
            }
            return false;
        }
        private static void AllocateLead(LeadDetails lead, string AssignmentProcess = "")
        {

            bool IsAssigned = false;

            if (lead.AssignedToGroup == 1 && lead.GroupID > 0)
            {
                lead.AssigntoUserID = 0;
                AllocationDLL.AssignLead(lead);
            }
            else if (lead.AssigntoUserID > 0)
            {
                if (lead.OutLeadRank > 0)
                    lead.LeadRank = lead.OutLeadRank;
                SetUserProperties(lead.AssigntoUserID, lead, AssignmentProcess);
                if (lead.UTM_Medium == "REASSIGN" || AssignmentProcess == "UHNI" || AssignmentProcess == "CTC")
                    AllocationDLL.ReAssignLead(lead);
                else
                    AllocationDLL.AssignLead(lead);
                    
                if (AssignmentProcess != "CTC" || lead.JobID != 12)
                {
                    AllocationDLL.UpdateAgentAllocationCounter(lead.LeadId, lead.AssigntoUserID, lead.ProductID, lead.LeadRank, lead.GroupCode);
                }
                IsAssigned = true;
                UpdateLeadDataState(lead, LeadDataStatus.LeadAssigned);
            }
            AllocationDLL.UpDateProcessedLead(lead, 0, false, IsAssigned);
        }

        #region Payu methods
        public static short GetPayULeadRank(string mobileNo, string email, long leadId)
        {
            short payULeadRank = -1;
            short dumpedPropensityScore = -2;
            short propensityScore;

            // get previous value
            if (payUPropensityCache.ContainsKey(leadId))
            {
                dumpedPropensityScore = payUPropensityCache.GetValueOrDefault(leadId);
            }

            // check from cache
            if (dumpedPropensityScore >= -1 && dumpedPropensityScore <= 10)
            {
                propensityScore = dumpedPropensityScore;
            }
            else
            {
                // get from API
                propensityScore = GetPayUPropensityAPI(mobileNo, email, leadId);
                payUPropensityCache.TryAdd(leadId, propensityScore);
            }

            if (propensityScore != -1 && payUPropensityAndLeadRankMapping.ContainsKey(propensityScore))
            {
                payULeadRank = payUPropensityAndLeadRankMapping.GetValueOrDefault(propensityScore);
            }

            return payULeadRank;
        }

        public static short GetPayULeadRankV2(LeadDetails lead)
        {

            short payULeadRank = -1;

            PayuScores scores = new()
            {
                affluence_score = -2,
                pbhealth_gi_propensity_tier_calibrated = -2
            };

            // get previous stored value from customerFeature if any
            if (lead.payu_affluence_score>-2 && lead.payu_propensity_score>-2)
            {
                scores.affluence_score = lead.payu_affluence_score;
                scores.pbhealth_gi_propensity_tier_calibrated = lead.payu_propensity_score;
            }
            else
            {
                scores = GetPayuScoreData(lead.CustomerId, lead.MobileNo, lead.EmailId);
                lead.payu_affluence_score=scores.affluence_score;
                lead.payu_propensity_score=scores.pbhealth_gi_propensity_tier_calibrated;
            }

            if (
                scores.affluence_score >= -1 && scores.pbhealth_gi_propensity_tier_calibrated >= -1
                && (lead.LeadRank>390 || lead.LeadRank<388)
                )
            {
                var PayuScoreRankMapping = GetPayuScoreRankMapping();
                string key = scores.affluence_score + "_" + scores.pbhealth_gi_propensity_tier_calibrated + "_" + lead.LeadRank;
                if (PayuScoreRankMapping.ContainsKey(key))
                {
                    PayuScoreRankMapping.TryGetValue(key, out payULeadRank);
                }
            }
            LoggingHelper.LoggingHelper.AddloginQueue("", lead.LeadId, string.Empty, "PayULeadRankV2", "Allocation", "AllocationBll"
                , scores.affluence_score + "_" + scores.pbhealth_gi_propensity_tier_calibrated + "_" + lead.LeadRank
                , payULeadRank.ToString(), DateTime.Now, DateTime.Now);


            return payULeadRank;
        }

        private static Dictionary<string, short> GetPayuScoreRankMapping()
        {
            Dictionary<string, short> payUScoreRankMapping = new();

            string cacheKey = "payUScoreRankMapping";
            if (MemoryCache.Default[cacheKey] != null)
            {
                payUScoreRankMapping = (Dictionary<string, short>)MemoryCache.Default.Get(cacheKey);
                return payUScoreRankMapping;
            }


            DataSet ds = AllocationDLL.GetPayUScoreRankMapping();

            if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
            {
                foreach (DataRow row in ds.Tables[0].Rows)
                {
                    string key = row["AffluenceScore"] + "_" + row["PropensityScore"] + "_" + row["PbRank"];
                    short finalRank = (short)row["FinalRank"];
                    payUScoreRankMapping.TryAdd(key, finalRank);
                }
            }
            CommonCache.GetOrInsertIntoCache(payUScoreRankMapping, cacheKey, 1 * 60);

            return payUScoreRankMapping;

        }

        public static PayuScores GetPayuScoreData(long CustomerId, string mobileNo, string email, string mobileNoHash = "")
        {
            DataTable dt = AllocationDLL.GetCustomerFeatureData(CustomerId);
            PayuScores scores = new()
            {
                affluence_score = -2,
                pbhealth_gi_propensity_tier_calibrated = -2
            };

            if (dt != null)
            {
                foreach (DataRow dr in dt.Rows)
                {

                    switch (dr["Feature"])
                    {
                        case CustomerFeature.affluenceScore:
                            scores.affluence_score = (int)dr["Value"];
                            break;

                        case CustomerFeature.propensity:
                            scores.pbhealth_gi_propensity_tier_calibrated = (int)dr["Value"];
                            break;
                        default:
                            break;
                    }
                }
            }

            // if scores not available in db, callapi
            if (scores.pbhealth_gi_propensity_tier_calibrated == -2 && scores.affluence_score == -2)
            {
                if (payuExperimentFlagHr != DateTime.Now.Hour)
                {
                    payuExperimentFlag = 0;
                    payuExperimentFlagHr = DateTime.Now.Hour;
                }
                PayuScoresResponse scoresResponse = GetScoresFromPayu(mobileNo, email, CustomerId, mobileNoHash);
                payuExperimentFlag++;
                //Random rnd = new Random();

                //PayuScoresResponse scoresResponse = new()
                //{
                //    data = new()
                //    {
                //        affluence_score = rnd.Next(-1, 10),
                //        pbhealth_gi_propensity_tier_calibrated = rnd.Next(-1, 5)
                //    }
                //};

                if (scoresResponse != null && (scoresResponse.message == "Data Fetched Successfully!!" || scoresResponse.message == "Not Found"))
                {
                    scores = scoresResponse.data;
                    SavePayUScoreData(CustomerId, scores);
                }


            }

            return scores;
        }

        public static void SavePayUScoreData(long CustomerId, PayuScores scores)
        {
            DateTime requestTime = DateTime.Now;
            try
            {
                AllocationDLL.SaveCustomerFeatureData(CustomerId, CustomerFeature.affluenceScore, scores.affluence_score);
                AllocationDLL.SaveCustomerFeatureData(CustomerId, CustomerFeature.propensity, scores.pbhealth_gi_propensity_tier_calibrated);
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", CustomerId, ex.ToString(), "SavePayUScoreData", "Allocation", "", JsonConvert.SerializeObject(scores), "", requestTime, DateTime.Now);
            }
        }

        public static short GetPayUPropensityAPI(string mobileNo, string emailId, long LeadId = 0)
        {
            DateTime requestTime = DateTime.Now;
            List<KeyValuePair<string, string>> FormData = null;
            string error = string.Empty;
            string response = string.Empty;

            try
            {
                emailId = emailId.ToLower();
                //string URL = "Payu_APIUrl_QA".AppSettings() + "daas/";
                //Dictionary<object, object> header = new() {
                //    { "API-KEY", "Payu_APIKey_QA".AppSettings() }, { "API-TOKEN", "Payu_APIToken_QA".AppSettings() }
                //};
                string URL = "Payu_APIUrl".AppSettings() + "daas/";
                Dictionary<object, object> header = new() {
                    { "API-KEY", "Payu_APIKey".AppSettings() }, { "API-TOKEN", "Payu_APIToken".AppSettings() }
                };

                FormData = new List<KeyValuePair<string, string>>
                {
                    new KeyValuePair<string, string>("mobile", Crypto.ComputeSHA256Hash(mobileNo)),
                    new KeyValuePair<string, string>("email", Crypto.ComputeSHA256Hash(emailId))
                };

                //FormData = new List<KeyValuePair<string, string>>
                //{
                //    new KeyValuePair<string, string>("mobile", "da7f5240ff54b5cdbaa9e99be6fd84b26cd6887abdaecfe818cc0b6edf2d4444"),
                //    new KeyValuePair<string, string>("email", "59cc86ead166eb8f999645de4fd302ad6ecda445ade3303577eb175a57fe131a"),
                //};

                response = CommonAPICall.PostAPICall_FormDataAsync(URL, FormData, header, 2000);
                dynamic _data = null;
                if (response != null) _data = JsonConvert.DeserializeObject<dynamic>(response);

                if (_data != null && _data.message == "Data Fetched Successfully!!" && _data.data != null && _data.data.pbhealth_gi_propensity_tier_calibrated != null)
                {
                    return (short)Convert.ToInt64(_data.data.pbhealth_gi_propensity_tier_calibrated);
                }
                else
                {
                    return -1;
                }
            }
            catch (Exception ex)
            {
                error = ex.ToString();
                return -1;
            }
            finally
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", LeadId, error, "GetPayUPropensityAPI", "Allocation", "", JsonConvert.SerializeObject(FormData), response, requestTime, DateTime.Now);
            }
        }

        public static PayuScoresResponse GetScoresFromPayu(string mobileNo, string emailId, long TrackingId = 0, string mobileNoHash = "")
        {
            DateTime requestTime = DateTime.Now;
            List<KeyValuePair<string, string>> FormData = null;
            Dictionary<object, object> header = new();
            string error = string.Empty;
            PayuScoresResponse response = new()
            {
                data = new()
                {
                    affluence_score = -1,
                    pbhealth_gi_propensity_tier_calibrated = -1
                }
            };

            try
            {
                //emailId = emailId.ToLower();

                string URL = "Payu_APIUrl".AppSettings() + "daas/";
                header = new() {
                    { "API-KEY", "Payu_APIKey".AppSettings() }, { "API-TOKEN", "Payu_APIToken".AppSettings() }
                };

                FormData = new List<KeyValuePair<string, string>>
                {
                    new KeyValuePair<string, string>("mobile", !string.IsNullOrEmpty(mobileNoHash) ? mobileNoHash : Crypto.ComputeSHA256Hash(mobileNo))
                    //new KeyValuePair<string, string>("email", Crypto.ComputeSHA256Hash(emailId))
                };

                // QA code
                //string URL = "https://mars-dev.payu.in/api/v3/daas/";
                //Dictionary<object, object> header = new() {
                //    { "API-KEY", "iJugUMrqDRCjiYNoENGLdahYzzAbEIuwuzpUCidDvbaWlqyGVKqzvhqkoQxJnkvn" }, { "API-TOKEN", "w6q3tc4il6s5qafk1edx8nwun734epol5xmotjhkd8rvynj9l7hrov6pvhel42hn" }
                //};
                //FormData = new List<KeyValuePair<string, string>>
                //{
                //    new KeyValuePair<string, string>("mobile", "da7f5240ff54b5cdbaa9e99be6fd84b26cd6887abdaecfe818cc0b6edf2d4444"),
                //    new KeyValuePair<string, string>("email", "ba249e64fe474f1ee3046c9ff4a0801701a163216ee7d5e7a41dda56533782b1"),
                //};

                string resStr = CommonAPICall.PostAPICall_FormDataAsync(URL, FormData, header, 2000);

                if (resStr != null) response = JsonConvert.DeserializeObject<PayuScoresResponse>(resStr);


                return response;
            }
            catch (Exception ex)
            {
                error = ex.ToString();
                return response;
            }
            finally
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", TrackingId, error, "GetScoresFromPayu", "Allocation", "", JsonConvert.SerializeObject(FormData) + JsonConvert.SerializeObject(header), JsonConvert.SerializeObject(response), requestTime, DateTime.Now);
            }
        }

        
        public bool GetPayuScoresForAllLeads()
        {
            try {
                var documents = AllocationDLL.GetSampleLeadsForPayuScore();

                if(documents != null ){
                    foreach (var document in documents)
                    {
                        string customerId = document.CustomerId.ToString();
                        string leadid = document.Leadid.ToString();
                        string mobileNoHash = document.mobileNoHash.ToString().ToLower();

                        if(string.IsNullOrEmpty(leadid) && string.IsNullOrEmpty(mobileNoHash))
                        {
                            var callingData = GetCallableNumber(Convert.ToInt64(customerId));
                            if (callingData != null && !string.IsNullOrEmpty(callingData.MobileNumber))
                            {
                                mobileNoHash = Crypto.ComputeSHA256Hash(callingData.MobileNumber);
                            }
                        }
                        else if (string.IsNullOrEmpty(mobileNoHash))
                        {
                            DataSet ds = AllocationDLL.GetCustDetails(customerId.ToString(), leadid.ToString());

                            if (ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0)
                            {
                                mobileNoHash = ds.Tables[0].Rows[0]["MobileNo"] == DBNull.Value ? string.Empty : Crypto.ComputeSHA256Hash(Convert.ToString(ds.Tables[0].Rows[0]["MobileNo"]));
                            }
                        }

                        GetPayuScoreData(Convert.ToInt64(customerId), "", "", mobileNoHash);
                    }
                    return true;
                }
                else {
                    return false;
                }
                
            } catch (Exception ex){
                Console.WriteLine(ex);
                return false;
            }
        }


        public bool GetPayuIncomeForAllLeads()
        {
            try {
                var documents = AllocationDLL.GetSampleLeadsForPayuIncome();

                if(documents != null ){
                    foreach (var document in documents)
                    {
                        string customerId = document.CustomerId.ToString();

                        DataSet payUIncomeDataset = TermAllocationDLL.GetTermLeadsPayUIncome(Convert.ToInt64(customerId));

                        if (payUIncomeDataset == null)
                        {
                            string mobileNo = string.Empty;

                            var callingData = GetCallableNumber(Convert.ToInt64(customerId));
                            if (callingData != null && !string.IsNullOrEmpty(callingData.MobileNumber))
                            {
                                mobileNo = callingData.MobileNumber;
                            }

                            string DerivedFrom = "NA";
                            long AnnualIncome = 0;
                            long PredictedIncome = TermPayURanksBLL.GetIncomeFromPayUAPI(mobileNo, "", -Convert.ToInt64(customerId));

                            if (PredictedIncome > 0)
                            {
                                AnnualIncome = PredictedIncome * 12;
                                DerivedFrom = "PayU";
                            }
                            
                            TermPayURanksDLL.Insert2LacLeadsPushtoTerm(-Convert.ToInt64(customerId), AnnualIncome, Convert.ToInt64(customerId), DerivedFrom);
                        }
                    }
                    return true;
                }
                else {
                    return false;
                }
                
            } catch (Exception ex){
                Console.WriteLine(ex);
                return false;
            }
        }

        public static CallingData GetCallableNumber(long CustomerID)
        {
            CallingData obj = null;
            DateTime requestTime = DateTime.Now;
            string url = "coreAPIv1".AppSettings();
            string data = string.Empty;
            try
            {
                string CustID = Crypto.Encrytion_Payment_AES(CustomerID.ToString(), "Core", 256, 128, "coreAPIencKey".AppSettings(), "coreAPIivKey".AppSettings());
                if (!string.IsNullOrEmpty(CustID))
                {
                    url = url + "cs/mtx/getCallableNumber?customerId=" + CustID;
                    Dictionary<object, object> _Dict = new()
                    {
                        { "authKey", "coreAPIauthKey".AppSettings() },
                        { "clientKey", "coreAPIclientKey".AppSettings() }
                    };

                    data = CommonAPICall.CallAPI(url, "", "GET", 1000, "application/json", _Dict);
                    if (!string.IsNullOrEmpty(data))
                    {
                        obj = JsonConvert.DeserializeObject<CallingData>(data);
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", CustomerID, ex.ToString(), "GetCallableNumber", "Allocation", "", url, data, requestTime, DateTime.Now);
            }

            return obj;
        }
        
        #endregion

        private void HidePIIFromLeads(List<LeadDetails> leadList)
        {
            foreach (var lead in leadList)
            {
                lead.MobileNo = "";
                lead.EmailId = "";
                lead.MobileSeries = 0;

            }
        }
        #region Utility
        private static List<FOSReopenLeadConfig> GetFosReopenLeadConfig()
        {
            string Key = "FOSReopenLeadConfigList";
            List<FOSReopenLeadConfig> FOSReopenLeadConfigList = new();

            if (MemoryCache.Default[Key] != null)
            {
                FOSReopenLeadConfigList = (List<FOSReopenLeadConfig>)MemoryCache.Default.Get(Key);
                return FOSReopenLeadConfigList;
            }

            DataSet ds = AllocationDLL.GetFosReopenLeadConfig();
            if (ds != null && ds.Tables[0].Rows.Count != 0)
            {
                foreach (DataRow row in ds.Tables[0].Rows)
                {
                    FOSReopenLeadConfig fOSReopenLeadConfig = new()
                    {
                        FRLCId = (row["ID"] != null && row["ID"] != DBNull.Value) ? (long)row["ID"] : 0,
                        ReopenProcess = (row["ReopenProcess"] != null && row["ReopenProcess"] != DBNull.Value) ? row["ReopenProcess"].ToString() : String.Empty,
                        CityID = (row["CityId"] != null && row["CityId"] != DBNull.Value) ? (int)row["CityId"] : 0,
                        GroupIds = (row["GroupIds"] != null && row["GroupIds"] != DBNull.Value) ? row["GroupIds"].ToString() : String.Empty,
                        ProductID = (row["ProductID"] != null && row["ProductID"] != DBNull.Value) ? (short)row["ProductID"] : (short)0
                    };

                    FOSReopenLeadConfigList.Add(fOSReopenLeadConfig);
                }
            }

            CommonCache.GetOrInsertIntoCache(FOSReopenLeadConfigList, Key, 8 * 60);
            return FOSReopenLeadConfigList;
        }

        private static void PurgeAssignedLeads()
        {
            // Console.WriteLine($"PurgeAssignedLeads1 : {leads.Count}");

            // purge leads which are assigned and older than 1 hour
            leads.RemoveAll(lead => (lead.dataState == LeadDataStatus.LeadAssigned && lead.CreatedOn < DateTime.Now.AddHours(-1)));
            churnedLeadsForAllocation.RemoveAll(lead => (lead.dataState == LeadDataStatus.LeadAssigned));
            // Console.WriteLine($"PurgeAssignedLeads2 : {leads.Count}");

        }

        public static void UpdateLeadDataState(LeadDetails lead, LeadDataStatus state)
        {
            lead.dataState = state;
        }

        public static void UpdateLastLeadDate(LeadDetails leadData)
        {
            if (leadData.CreatedOn > lastLeadDateTime)
            {
                lastLeadDateTime = leadData.CreatedOn;
            }
        }

        public static int IsLeadDataAvailable(LeadDetails lead, List<LeadDetails> leadList)
        {

            //for (int i = 0; i < leads.Count; i++)
            //{
            //    if (leads[i].LeadId == lead.LeadId)
            //    {
            //        return i;
            //    }
            //}
            //return -1;

            CompareLeadId comparator = new();

            int index = leadList.BinarySearch(lead, comparator);
            return index;

            //if (index < 0)
            //{
            //    leads.Insert(~index, lead);
            //}

            // todo: use Binary search, check if list is sorted on leadid
            //CompareLeadId comparator = new();

            //return leads.BinarySearch(lead, comparator)>=0;

        }

        public static bool IsCorrectTimeToCall(string Country, string leadCity = null)
        {
            DateTime CurrentDate = DateTime.Now;
            DateTime requestTime = DateTime.Now;
            DateTime startTime;
            DateTime EndTime;

            try
            {
                // check Timezone for NRI city
                var CityTimeZoneDict = AllocationDLL.GetCityTimeZone();
                if (!string.IsNullOrEmpty(leadCity) && CityTimeZoneDict.ContainsKey(leadCity))
                {
                    NriCityTimeZone cityTZ = CityTimeZoneDict.GetValueOrDefault(leadCity);

                    if (cityTZ != null && (!string.IsNullOrEmpty(cityTZ.TimeDiffFromIST)))
                    {
                        if (cityTZ.StartTime > cityTZ.EndTime && CurrentDate.TimeOfDay < cityTZ.EndTime)
                            startTime = CurrentDate.Date.AddDays(-1).Add(cityTZ.StartTime);
                        else
                            startTime = CurrentDate.Date.Add(cityTZ.StartTime);
                        EndTime = cityTZ.StartTime < cityTZ.EndTime ? CurrentDate.Date.Add(cityTZ.EndTime) : CurrentDate.Date.AddDays(1).Add(cityTZ.EndTime);
                        if (startTime <= CurrentDate && EndTime >= CurrentDate)
                            return true;
                        return false;
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingHelper.LoggingHelper.AddloginQueue("", 0, ex.ToString(), "IsCorrectTimeToCall_City_error", "Allocation", "AllocationBLL", Country.ToString(), "", requestTime, DateTime.Now);
            }
            return true;
        }


        public string Test(string filePath, int? CityId)
        {
            var existingZones = AllocationDLL.GetAllZonesFromMongo();


            var newZones = LatLongHelper.ParseKML(filePath, CityId);
            List<CityZone> invalidZones = new();


            // GetCityList master
            // todo: cache citylist master, separate method
            var cityDataSet = AllocationDLL.GetCityList();
            Dictionary<int, CityModal> cityDict = new();
            if (cityDataSet != null && cityDataSet.Tables[0].Rows.Count != 0)
            {
                foreach (DataRow cityRow in cityDataSet.Tables[0].Rows)
                {
                    CityModal city = new()
                    {
                        CityId = (cityRow["CityId"] != null && cityRow["CityId"] != DBNull.Value) ? Convert.ToInt32(cityRow["CityId"]) : 0,
                        CityName = (cityRow["CityName"] != null && cityRow["CityName"] != DBNull.Value) ? cityRow["CityName"].ToString() : string.Empty,
                        DisplayName = (cityRow["DisplayName"] != null && cityRow["DisplayName"] != DBNull.Value) ? cityRow["DisplayName"].ToString() : string.Empty,
                        StateName = (cityRow["StateName"] != null && cityRow["StateName"] != DBNull.Value) ? cityRow["StateName"].ToString() : string.Empty,
                        StateId = (cityRow["StateId"] != null && cityRow["StateId"] != DBNull.Value) ? Convert.ToInt32(cityRow["StateId"]) : 0,
                    };
                    cityDict.Add(city.CityId, city);
                }
            }

            newZones.ForEach((newZone) =>
            {
                if (cityDict.ContainsKey(newZone.CityId))
                {
                    CityModal city = cityDict.GetValueOrDefault(newZone.CityId);
                    newZone.State = city.StateName;
                    newZone.City = city.CityName;
                    newZone.CityId = city.CityId;
                    newZone.StateId = city.StateId;

                    //check for already existing zoneId
                    CityZone matchingZone = existingZones.Find(ezone => ezone.ZoneId == newZone.ZoneId);
                    if (matchingZone == null)
                    {
                        // new zone
                    }
                    else if (matchingZone.CityId != newZone.CityId)
                    {
                        newZone.ZoneId = newZone.ZoneId + "-" + newZone.City;
                    }
                    else
                    {
                        Console.WriteLine("Zone Already exists for this city: " + JsonConvert.SerializeObject(newZone));
                        invalidZones.Add(newZone);
                    }
                }
                else
                {
                    Console.WriteLine("Invalid CityId: " + JsonConvert.SerializeObject(newZone));
                    invalidZones.Add(newZone);
                }
            });

            invalidZones.ForEach((invalidZone) => newZones.Remove(invalidZone));

            Coordinate pointCoordinate = new(28.63874, 77.03291);

            //// sample code for zone matching
            existingZones.ForEach((CityZone zone) =>
            {
                var result = zone.Coordinates.ContainsPoint(pointCoordinate);
                if (result)
                {
                    Console.WriteLine(zone.ZoneId);
                }
            });
            //// end sample code for zone matching 

            return JsonConvert.SerializeObject(newZones);
        }

        public short ClearCache(string key)
        {
            switch (key)
            {
                case "SpecialGroups":
                    if (_SpecialGroups != null)
                    {
                        _SpecialGroups = null;
                        return 1;
                    }
                    break;
                default:
                    return -2;
            }
            return 0;
        }
        #endregion Utility

    }

    public class CompareLeadId : IComparer<LeadDetails>
    {
        public int Compare(LeadDetails x, LeadDetails y)
        {
            if (x.LeadId == 0) return -1;
            if (y.LeadId == 0) return 1;

            return (int)(x.LeadId - y.LeadId);
        }
    }



}
